<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8d21f7d4-8372-470f-a2b7-572273ea40e8" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build.spec" beforeDir="false" afterPath="$PROJECT_DIR$/build.spec" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build_nuitka.py" beforeDir="false" afterPath="$PROJECT_DIR$/build_nuitka.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/check_python.bat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Common/WindowControls.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Common/WindowControls.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Layout/MainContent.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Layout/MainContent.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Layout/Sidebar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Layout/Sidebar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Settings/LanguageModal.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/components/Settings/LanguageModal.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/src/types/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/src/types/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-free-vip-vue/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/cursor-free-vip-vue/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/ar.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/bg.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/de.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/en.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/es.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/fr.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/it.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/ja.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/nl.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/pt.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/ru.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/tr.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/locales/vi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/main.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30Do5FgoVJ4enw2Dykh2aAXBwqo" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="cursor-free-vip-main" type="Python.FlaskServer">
      <module name="cursor-free-vip-main" />
      <option name="target" value="$PROJECT_DIR$/local_api_server.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8d21f7d4-8372-470f-a2b7-572273ea40e8" name="更改" comment="" />
      <created>1753171323197</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753171323197</updated>
      <workItem from="1753171324247" duration="5000" />
      <workItem from="1753171703981" duration="20000" />
      <workItem from="1753171814414" duration="96000" />
      <workItem from="1753173556055" duration="8000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>