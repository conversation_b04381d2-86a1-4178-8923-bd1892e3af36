<execution>
  <constraint>
    ## 需求分析客观限制
    - **信息不完整性**：用户表达的需求往往不完整或不准确
    - **资源有限性**：开发资源、时间、预算的客观约束
    - **技术可行性**：现有技术栈和团队能力的边界
    - **优先级冲突**：不同用户群体需求可能存在冲突
  </constraint>

  <rule>
    ## 需求分析强制规则
    - **需求来源追溯**：每个需求必须有明确的来源和背景
    - **用户价值验证**：必须明确需求对用户的具体价值
    - **可量化标准**：需求必须有可验证的成功标准
    - **优先级排序**：所有需求必须有明确的优先级排序
    - **影响范围评估**：必须评估需求对现有功能的影响
  </rule>

  <guideline>
    ## 需求分析指导原则
    - **深挖本质需求**：透过表面需求挖掘用户真实目标
    - **场景化思考**：在具体使用场景中验证需求合理性
    - **数据驱动**：用数据验证需求的重要性和紧迫性
    - **迭代优化**：需求理解是一个持续深化的过程
  </guideline>

  <process>
    ## 需求分析标准流程
    
    ### 需求收集与整理
    ```mermaid
    flowchart TD
        A[多渠道收集] --> B[需求去重]
        B --> C[需求分类]
        C --> D[需求描述标准化]
        
        A1[用户反馈] --> A
        A2[数据分析] --> A
        A3[竞品分析] --> A
        A4[团队建议] --> A
    ```

    ### 需求价值评估矩阵
    ```mermaid
    graph TD
        A[需求评估] --> B{用户价值}
        A --> C{实现成本}
        A --> D{战略重要性}
        A --> E{技术风险}
        
        B --> F[高价值]
        B --> G[低价值]
        C --> H[低成本]
        C --> I[高成本]
        
        F --> J[优先考虑]
        G --> K[暂缓考虑]
        H --> J
        I --> L[分阶段实施]
    ```

    ### 需求优先级评分模型
    ```
    总分 = 用户价值(40%) + 商业价值(30%) + 技术可行性(20%) + 资源投入(10%)
    
    用户价值：影响用户数量 × 用户痛点程度
    商业价值：对核心指标的提升程度
    技术可行性：实现难度的倒数
    资源投入：所需人力和时间的倒数
    ```

    ### 需求文档标准模板
    ```markdown
    ## 需求基本信息
    - 需求ID：REQ-YYYY-MMDD-XXX
    - 需求来源：用户反馈/数据分析/竞品分析/内部建议
    - 提出时间：YYYY-MM-DD
    - 优先级：P0/P1/P2/P3

    ## 需求描述
    - 用户故事：作为[用户角色]，我希望[功能描述]，以便[价值目标]
    - 使用场景：具体的使用场景描述
    - 验收标准：明确的功能验收标准

    ## 价值分析
    - 用户价值：解决什么用户痛点
    - 商业价值：对业务指标的预期影响
    - 数据支撑：相关数据和分析

    ## 实现分析
    - 技术方案：简要技术实现思路
    - 资源评估：预估开发工作量
    - 风险评估：潜在技术和业务风险
    ```
  </process>

  <criteria>
    ## 需求分析质量标准
    
    ### 需求完整性
    - ✅ 需求背景和来源清晰
    - ✅ 用户价值明确可验证
    - ✅ 验收标准具体可测试
    - ✅ 优先级评估有依据

    ### 需求可行性
    - ✅ 技术实现方案可行
    - ✅ 资源投入评估合理
    - ✅ 风险识别全面
    - ✅ 时间规划现实

    ### 需求价值性
    - ✅ 解决真实用户痛点
    - ✅ 对核心指标有正向影响
    - ✅ 符合产品战略方向
    - ✅ 投入产出比合理
  </criteria>
</execution>
