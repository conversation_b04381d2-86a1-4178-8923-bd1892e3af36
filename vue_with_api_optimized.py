#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro - 简化优化版
只保留真正有用的优化：模块缓存 + 简单缓存
"""

import json
import sys
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import logging

# 版本控制 - 在所有其他导入之前 (严格模式)
try:
    from version_control import check_version_before_start
    check_version_before_start()
except ImportError:
    print("❌ 版本控制模块未找到，程序无法启动")
    print("❌ 请确保 version_control.py 文件存在")
    sys.exit(1)
except SystemExit:
    # 版本检查主动退出，不要捕获
    raise
except Exception as e:
    print(f"❌ 版本检查失败: {e}")
    print("❌ 由于版本检查异常，程序无法启动")
    sys.exit(1)

# 简化日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入新的数据路径管理系统
try:
    from legacy_path_adapter import get_legacy_adapter, read_file, write_file, append_file, file_exists, get_file_path
    logger.info("✅ 数据路径管理系统导入成功")
except ImportError as e:
    logger.warning(f"⚠️ 数据路径管理系统导入失败: {e}")
    logger.warning("⚠️ 将使用传统文件路径方式")
    # 提供兼容性函数
    def get_file_path(filename): return filename
    def read_file(filename, encoding='utf-8', default=''):
        try:
            with open(filename, 'r', encoding=encoding) as f:
                return f.read()
        except:
            return default
    def write_file(filename, content, encoding='utf-8'):
        try:
            with open(filename, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except:
            return False
    def append_file(filename, content, encoding='utf-8'):
        try:
            with open(filename, 'a', encoding=encoding) as f:
                f.write(content)
            return True
        except:
            return False
    def file_exists(filename):
        import os
        return os.path.exists(filename)

# 模块缓存 - 这个确实有用，提升99%性能
_module_cache = {}

def import_module_cached(module_name):
    """缓存模块导入，避免重复导入"""
    if module_name not in _module_cache:
        try:
            _module_cache[module_name] = __import__(module_name)
            logger.info(f"✅ 模块 {module_name} 导入成功")
        except ImportError as e:
            logger.error(f"❌ 模块 {module_name} 导入失败: {e}")
            _module_cache[module_name] = None
    return _module_cache[module_name]

class SimpleOptimizedAPI:
    """简化优化版API - 只保留有用的功能"""
    
    def __init__(self):
        # 直接集成必要的功能模块，不依赖外部导入
        self._init_modules()

        # 简单的结果缓存
        self._cache = {}
        self._cache_time = {}
        self.cache_duration = 30  # 30秒缓存

        logger.info("✅ 简化优化版API初始化完成")

    def _init_modules(self):
        """初始化必要的功能模块"""
        try:
            # 导入基础模块
            import cursor_acc_info
            import reset_machine_manual
            import os
            import platform
            import glob
            from pathlib import Path
            import bypass_version
            import bypass_token_limit
            import new_signup
            import oauth_auth

            # 导入SQLite认证模块
            import cursor_auth
            self.cursor_auth_class = cursor_auth.CursorAuth
            logger.info("✅ 使用SQLite认证模块")

            # 存储模块引用
            self.cursor_acc_info = cursor_acc_info
            self.reset_machine_manual = reset_machine_manual
            self.bypass_version = bypass_version
            self.bypass_token_limit = bypass_token_limit
            self.new_signup = new_signup
            self.oauth_auth = oauth_auth

            logger.info("✅ 成功加载所有功能模块")

        except Exception as e:
            logger.error(f"❌ 加载功能模块失败: {e}")
            # 设置为None，后续方法会处理

    # 公共API方法（通过缓存调用）
    def get_account_info(self):
        """获取账户信息"""
        return self._call_with_cache('get_account_info')

    def get_registered_accounts(self):
        """获取注册账户列表"""
        return self._call_with_cache('get_registered_accounts')

    def show_config(self):
        """显示配置信息"""
        return self._call_with_cache('show_config')

    def get_log(self):
        """获取日志"""
        return self._call_with_cache('get_log')

    # 实际API方法实现（避免递归调用）
    def _get_account_info_impl(self):
        """获取账户信息的实际实现"""
        try:
            # 导入当前目录的模块
            import cursor_acc_info

            # 获取token来判断登录状态
            token = cursor_acc_info.get_token()
            if not token:
                return {"error": "未登录", "message": "请先登录Cursor"}

            # 获取路径信息
            paths = cursor_acc_info.get_token_from_config()
            if not paths:
                return {"error": "配置错误", "message": "无法获取配置路径"}

            # 优先从本地文件获取email - 模仿初始项目逻辑
            email = cursor_acc_info.get_email_from_storage(paths['storage_path'])

            # 如果storage中没找到，尝试从sqlite获取
            if not email:
                email = cursor_acc_info.get_email_from_sqlite(paths['sqlite_path'])

            # 获取订阅信息
            subscription_info = None
            try:
                usage_manager = cursor_acc_info.UsageManager()
                subscription_info = usage_manager.get_stripe_profile(token)
            except Exception as e:
                logger.error(f"获取订阅信息失败: {e}")

            # 如果本地都没找到email，尝试从订阅信息获取
            if not email and subscription_info:
                if 'customer' in subscription_info and 'email' in subscription_info['customer']:
                    email = subscription_info['customer']['email']

            # 获取使用情况信息
            usage_info = None
            try:
                usage_info = usage_manager.get_usage(token)
            except Exception as e:
                logger.error(f"获取使用情况失败: {e}")

            # 如果有email说明已登录，返回账户信息
            if email:
                # 获取试用天数
                trial_days = 0
                if subscription_info and 'daysRemainingOnTrial' in subscription_info:
                    trial_days = subscription_info['daysRemainingOnTrial']

                # 获取使用情况数据
                pro_used = 0
                pro_total = 999
                pro_percent = 0
                basic_total = 'No Limit'

                if usage_info:
                    pro_used = usage_info.get('premium_usage', 0)
                    pro_total = usage_info.get('max_premium_usage', 999)
                    basic_total = usage_info.get('max_basic_usage', 'No Limit')

                    # 计算百分比
                    if pro_total and pro_total != 'No Limit':
                        pro_percent = round((pro_used / pro_total) * 100, 1)

                return {
                    "email": email,
                    "plan": cursor_acc_info.format_subscription_type(subscription_info) if subscription_info else "Free",
                    "trial_days": trial_days,
                    "pro_used": pro_used,
                    "pro_total": pro_total,
                    "pro_percent": pro_percent,
                    "basic_total": basic_total
                }
            else:
                return {"error": "未登录", "message": "未找到登录信息"}

        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return {"error": str(e)}

    def _get_registered_accounts_impl(self):
        """获取注册账户列表的实际实现"""
        try:
            import os
            import re

            accounts = []

            # 读取账户文件 (cursor_accounts.txt) - 使用新的路径管理系统
            content = read_file('cursor_accounts.txt')
            if content:

                # 按分隔符分割账户信息块
                account_blocks = content.split('=' * 50)

                for block in account_blocks:
                    if not block.strip():
                        continue

                    account = {}
                    lines = block.strip().split('\n')

                    for line in lines:
                        if ':' in line:
                            key, value = line.split(':', 1)
                            key = key.strip()
                            value = value.strip()

                            if key == 'Email':
                                account['email'] = value
                            elif key == 'Password':
                                account['password'] = value
                            elif key == 'Token':
                                account['token'] = value
                            elif key == 'Plan':
                                account['plan'] = value
                            elif key == 'Trial Days':
                                account['trial_days'] = int(value) if value.isdigit() else 0
                            elif key == 'Pro Used':
                                account['pro_used'] = int(value) if value.isdigit() else 0
                            elif key == 'Pro Total':
                                account['pro_total'] = int(value) if value.isdigit() else 0
                            elif key == 'Basic Total':
                                account['basic_total'] = value
                            elif key == 'Saved Time':
                                account['saved_time'] = value
                            elif key == 'Source':
                                account['source'] = value

                    if account.get('email') and '@' in account['email']:
                        # 设置默认值
                        account.setdefault('status', 'active')
                        account.setdefault('source', 'registered')
                        account.setdefault('plan', 'Free')
                        account.setdefault('trial_days', 0)
                        account.setdefault('pro_used', 0)
                        account.setdefault('pro_total', 0)
                        account.setdefault('basic_total', 'No Limit')

                        accounts.append(account)

            # 获取当前登录账户信息，设置正确的状态
            try:
                current_account_info = self._get_account_info_impl()
                current_email = current_account_info.get('email') if 'error' not in current_account_info else None

                # 为所有账户设置状态
                for account in accounts:
                    if current_email and account['email'] == current_email:
                        account['isActive'] = True
                        account['status'] = 'active'
                    else:
                        account['isActive'] = False
                        account['status'] = 'inactive'
            except Exception as e:
                logger.warning(f"设置账户状态失败: {e}")
                # 如果获取当前账户失败，所有账户都设为非活跃
                for account in accounts:
                    account['isActive'] = False
                    account['status'] = 'inactive'

            logger.info(f"解析到 {len(accounts)} 个账户")
            return accounts

        except Exception as e:
            logger.error(f"获取注册账户失败: {e}")
            return []

    def _show_config_impl(self):
        """显示配置信息的实际实现"""
        try:
            import config
            return f"配置加载成功 - 版本: {getattr(config, 'VERSION', '未知')}"
        except Exception as e:
            return f"配置加载失败: {e}"

    def _get_log_impl(self):
        """获取日志的实际实现"""
        try:
            if hasattr(self, 'reset_machine_manual'):
                # 获取实际的日志内容
                log_content = self.reset_machine_manual.get_log()
                return log_content if log_content else "暂无日志内容"
            else:
                # 尝试直接导入并获取日志
                try:
                    import reset_machine_manual
                    log_content = reset_machine_manual.get_log()
                    return log_content if log_content else "暂无日志内容"
                except ImportError:
                    return "日志系统初始化中..."
        except Exception as e:
            return f"获取日志失败: {e}"

    def _delete_account_impl(self, email=None):
        """删除账户的实际实现"""
        try:
            import os
            import shutil
            from datetime import datetime

            # 使用新的路径管理系统
            if not file_exists('cursor_accounts.txt'):
                return {"error": "账户文件不存在"}

            # 读取文件内容
            content = read_file('cursor_accounts.txt')
            if not content.strip():
                return {"error": "账户文件为空"}

            # 解析账户块
            account_blocks = content.split('=' * 50)
            new_blocks = []
            deleted_block = None
            deleted_email = None

            for block in account_blocks:
                if not block.strip():
                    continue

                # 检查是否包含要删除的邮箱
                if email and f"Email: {email}" in block:
                    deleted_email = email
                    # 保存被删除的账户块
                    deleted_block = '=' * 50 + '\n' + block.strip() + '\n' + '=' * 50 + '\n\n'
                    continue  # 跳过这个账户块
                else:
                    # 保留其他账户块
                    if 'Email:' in block:
                        new_blocks.append('=' * 50 + block)

            if not deleted_email:
                return {"error": f"未找到邮箱为 {email} 的账户"}

            # 将被删除的账户追加到备份文件
            backup_file = 'cursor_accounts_backup.txt'
            if deleted_block:
                # 确保备份文件存在
                if not file_exists('cursor_accounts_backup.txt'):
                    write_file('cursor_accounts_backup.txt', "# Cursor 账号备份文件\n# 此文件用于存储被删除的账号，方便恢复\n# 格式与 cursor_accounts.txt 相同\n\n")

                # 追加被删除的账户到备份文件
                append_file('cursor_accounts_backup.txt', deleted_block)

            # 写入新内容
            new_content = '\n'.join(new_blocks)
            if new_content.strip():
                new_content += '\n'

            write_file('cursor_accounts.txt', new_content)

            return {
                "success": True,
                "message": f"成功删除账户 {deleted_email}，已备份到 {backup_file}",
                "backup_file": backup_file
            }

        except Exception as e:
            return {"error": f"删除账户失败: {e}"}

    def _restore_account_impl(self, backup_file=None):
        """恢复账户的实际实现"""
        try:
            import os
            import shutil
            from datetime import datetime

            if not backup_file:
                return {"error": "未提供备份文件名"}

            # 检查备份文件是否存在
            if not os.path.exists(backup_file):
                return {"error": f"备份文件不存在: {backup_file}"}

            # 使用新的路径管理系统
            # 创建当前文件的备份
            if file_exists('cursor_accounts.txt'):
                current_content = read_file('cursor_accounts.txt')
                append_file('cursor_accounts_backup.txt', f"\n# 恢复前备份 {datetime.now()}\n{current_content}\n")

            # 从备份文件恢复
            backup_content = read_file(backup_file)
            write_file('cursor_accounts.txt', backup_content)

            # 恢复成功后删除备份文件
            try:
                os.remove(backup_file)
                deleted_backup = True
            except Exception as e:
                deleted_backup = False

            return {
                "success": True,
                "message": f"成功从备份文件恢复账户，备份文件已自动删除",
                "backup_file": backup_file,
                "current_backup": current_backup if os.path.exists(target_file) else None,
                "backup_deleted": deleted_backup
            }

        except Exception as e:
            return {"error": f"恢复账户失败: {e}"}

    def restore_account_from_backup(self, email=None):
        """从备份文件中恢复指定账号"""
        try:
            import os
            from datetime import datetime

            if not email:
                return {"success": False, "error": "邮箱地址不能为空"}

            # 使用新的路径管理系统
            if not file_exists('cursor_accounts_backup.txt'):
                return {"success": False, "error": "备份文件不存在"}

            # 读取备份文件
            backup_content = read_file('cursor_accounts_backup.txt')

            # 解析备份文件中的账户块
            backup_blocks = backup_content.split('=' * 50)
            account_to_restore = None
            remaining_backup_blocks = []

            for block in backup_blocks:
                if not block.strip() or block.strip().startswith('#'):
                    continue

                # 检查是否是要恢复的账户
                if f"Email: {email}" in block:
                    account_to_restore = '=' * 50 + '\n' + block.strip() + '\n' + '=' * 50 + '\n\n'
                else:
                    # 保留其他备份账户
                    if 'Email:' in block:
                        remaining_backup_blocks.append('=' * 50 + '\n' + block.strip() + '\n' + '=' * 50 + '\n\n')

            if not account_to_restore:
                return {"success": False, "error": f"在备份文件中未找到邮箱为 {email} 的账户"}

            # 将账户添加到主文件
            append_file('cursor_accounts.txt', account_to_restore)

            # 更新备份文件，移除已恢复的账户
            if remaining_backup_blocks:
                backup_header = "# Cursor 账号备份文件\n# 此文件用于存储被删除的账号，方便恢复\n# 格式与 cursor_accounts.txt 相同\n\n"
                write_file('cursor_accounts_backup.txt', backup_header + ''.join(remaining_backup_blocks))
            else:
                # 如果备份文件为空，重置为初始状态
                write_file('cursor_accounts_backup.txt', "# Cursor 账号备份文件\n# 此文件用于存储被删除的账号，方便恢复\n# 格式与 cursor_accounts.txt 相同\n\n")

            return {"success": True, "message": f"成功恢复账户 {email}"}

        except Exception as e:
            return {"success": False, "error": f"恢复账户失败: {e}"}

    def list_backup_accounts(self):
        """列出备份文件中的所有账户"""
        try:
            import os

            backup_file = 'cursor_accounts_backup.txt'

            if not os.path.exists(backup_file):
                return {"success": True, "accounts": []}

            # 读取备份文件
            with open(backup_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析账户块
            account_blocks = content.split('=' * 50)
            accounts = []

            for block in account_blocks:
                if not block.strip() or block.strip().startswith('#'):
                    continue

                if 'Email:' in block:
                    lines = block.strip().split('\n')
                    account_info = {}

                    for line in lines:
                        if line.startswith('Email:'):
                            account_info['email'] = line.replace('Email:', '').strip()
                        elif line.startswith('Password:'):
                            account_info['password'] = line.replace('Password:', '').strip()
                        elif line.startswith('Token:'):
                            account_info['token'] = line.replace('Token:', '').strip()
                        elif line.startswith('Plan:'):
                            account_info['plan'] = line.replace('Plan:', '').strip()
                        elif line.startswith('Usage Limit:'):
                            account_info['usage_limit'] = line.replace('Usage Limit:', '').strip()

                    if account_info.get('email'):
                        accounts.append(account_info)

            return {"success": True, "accounts": accounts}

        except Exception as e:
            return {"success": False, "error": f"获取备份账户列表失败: {e}"}

    def _list_backups_impl(self):
        """列出所有备份文件"""
        try:
            import os
            import glob
            from datetime import datetime

            # 查找备份文件
            backup_file = 'cursor_accounts_backup.txt'
            backup_files = [backup_file] if os.path.exists(backup_file) else []

            # 获取文件信息
            backups = []
            for file in backup_files:
                try:
                    stat = os.stat(file)
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime)

                    # 解析文件名中的时间戳和动作类型
                    action = '备份'

                    backups.append({
                        'filename': file,
                        'size': size,
                        'modified': mtime.strftime('%Y-%m-%d %H:%M:%S'),
                        'action': action
                    })
                except Exception as e:
                    continue

            # 按修改时间排序（最新的在前）
            backups.sort(key=lambda x: x['modified'], reverse=True)

            return {
                "success": True,
                "backups": backups,
                "count": len(backups)
            }

        except Exception as e:
            return {"error": f"获取备份列表失败: {e}"}

    def _delete_account_backup_impl(self, backup_file=None):
        """删除账户备份文件"""
        try:
            import os

            if not backup_file:
                return {"error": "未提供备份文件名"}

            # 检查文件是否存在
            if not os.path.exists(backup_file):
                return {"error": f"备份文件不存在: {backup_file}"}

            # 删除文件
            os.remove(backup_file)

            return {
                "success": True,
                "message": f"成功删除备份文件: {backup_file}"
            }

        except Exception as e:
            return {"error": f"删除备份文件失败: {e}"}

    def _get_cached_result(self, key):
        """获取缓存结果"""
        if key in self._cache:
            if time.time() - self._cache_time.get(key, 0) < self.cache_duration:
                return self._cache[key]
            else:
                # 缓存过期，清理
                del self._cache[key]
                del self._cache_time[key]
        return None
    
    def _set_cached_result(self, key, result):
        """设置缓存结果"""
        self._cache[key] = result
        self._cache_time[key] = time.time()
    
    def _call_with_cache(self, method_name, cache_key=None):
        """带缓存的方法调用"""
        if cache_key is None:
            cache_key = method_name
        
        # 检查缓存
        cached = self._get_cached_result(cache_key)
        if cached is not None:
            logger.info(f"🚀 缓存命中: {method_name}")
            return cached
        
        # 调用对应的API方法（直接调用实现，避免递归）
        try:
            if method_name == 'get_account_info':
                result = self._get_account_info_impl()
            elif method_name == 'get_registered_accounts':
                result = self._get_registered_accounts_impl()
            elif method_name == 'show_config':
                result = self._show_config_impl()
            elif method_name == 'get_log':
                result = self._get_log_impl()
            elif method_name == 'delete_account':
                # delete_account 需要特殊处理，因为它需要参数
                result = {"error": "delete_account 需要通过POST请求调用"}
            elif method_name == 'get_config_files':
                result = self.get_config_files()
            else:
                result = {"error": f"未知方法: {method_name}"}

            # 缓存结果
            self._set_cached_result(cache_key, result)
            logger.info(f"✅ {method_name} 执行成功")
            return result

        except Exception as e:
            # 对于Cursor未登录错误，静默处理，不记录重复错误
            error_msg = str(e)
            if "未找到Cursor登录token" not in error_msg and "请先登录Cursor" not in error_msg:
                logger.error(f"❌ {method_name} 执行失败: {e}")
            return {"error": str(e)}
    
    # 查询类方法 - 使用缓存
    def get_account_info(self):
        """获取账户信息（带缓存）"""
        return self._call_with_cache('get_account_info')
    
    def get_registered_accounts(self):
        """获取注册账户列表（带缓存）"""
        return self._call_with_cache('get_registered_accounts')
    
    def get_log(self):
        """获取日志（带缓存）"""
        return self._call_with_cache('get_log')
    
    # 操作类方法 - 不缓存，但清除相关缓存
    def reset_machine_id_old(self):
        """重置机器ID（旧方法）"""
        self._cache.clear()
        self._cache_time.clear()
        try:
            if hasattr(self, 'reset_machine_manual'):
                return self.reset_machine_manual.reset_machine_id()
            else:
                import reset_machine_manual
                return reset_machine_manual.reset_machine_id()
        except Exception as e:
            logger.error(f"重置机器码失败: {e}")
            return {"success": False, "error": str(e)}

    def save_register_config(self, config_data):
        """保存注册配置"""
        try:
            import json
            import os

            # 获取配置目录
            config_dir = os.path.join(os.path.expanduser("~"), ".cursor-pro")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            config_file = os.path.join(config_dir, "register_config.json")

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            return {
                "success": True,
                "message": "注册配置保存成功",
                "config_file": config_file
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"保存注册配置失败: {e}"
            }

    def get_register_config(self):
        """获取注册配置"""
        try:
            import json
            import os

            config_file = os.path.join(os.path.expanduser("~"), ".cursor-pro", "register_config.json")

            if not os.path.exists(config_file):
                # 返回默认配置
                return {
                    "success": True,
                    "data": {
                        'email': '',
                        'tempmailDomain': '',
                        'tempmailEpin': '',
                        'firstName': '',
                        'lastName': '',
                        'password': '',
                        'region': 'US',
                        'registerMode': 'auto',
                        'showBrowser': False,
                        'timeout': 300,
                        'retryCount': 3
                    }
                }

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            return {
                "success": True,
                "data": config
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取注册配置失败: {e}"
            }

    def auto_register(self):
        """自动注册 - 模仿原版实现"""
        self._cache.clear()
        self._cache_time.clear()

        try:
            import json
            import os

            # 记录开始自动注册
            logs = []
            logs.append("[自动注册] 开始自动注册流程...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[自动注册] 开始自动注册流程...")

            # 获取注册配置
            config_file = os.path.join(os.path.expanduser("~"), ".cursor-pro", "register_config.json")
            if not os.path.exists(config_file):
                error_log = f"[自动注册] ❌ 配置文件不存在: {config_file}"
                logs.append(error_log)
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log(error_log)
                return {
                    "success": False,
                    "error": "注册配置不存在，请先在设置页面配置注册信息",
                    "logs": logs
                }

            logs.append(f"[自动注册] 📄 读取配置文件: {config_file}")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(f"[自动注册] 📄 读取配置文件: {config_file}")

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 记录配置信息（隐藏敏感信息）
            email = config.get('email', '')
            logs.append(f"[自动注册] 📧 注册邮箱: {email}")
            logs.append(f"[自动注册] 👤 注册姓名: {config.get('firstName', '')} {config.get('lastName', '')}")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(f"[自动注册] 📧 注册邮箱: {email}")
                self.reset_machine_manual.log(f"[自动注册] 👤 注册姓名: {config.get('firstName', '')} {config.get('lastName', '')}")

            # 添加原版目录到路径
            import sys
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            logs.append("[自动注册] 🔧 初始化注册模块...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[自动注册] 🔧 初始化注册模块...")

            # 导入原版模块
            import cursor_register_manual
            from main import translator as main_translator

            logs.append("[自动注册] 🚀 开始执行注册流程...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[自动注册] 🚀 开始执行注册流程...")

            # 调用原版注册功能
            result = cursor_register_manual.main(main_translator, config)

            if result:
                logs.append("[自动注册] ✅ 注册流程执行成功")
                logs.append(f"[自动注册] 📧 注册邮箱: {config.get('email', '')}")
                logs.append(f"[自动注册] 👤 账户姓名: {config.get('firstName', '')} {config.get('lastName', '')}")
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log("[自动注册] ✅ 注册流程执行成功")
                    self.reset_machine_manual.log(f"[自动注册] 📧 注册邮箱: {config.get('email', '')}")
                    self.reset_machine_manual.log(f"[自动注册] 👤 账户姓名: {config.get('firstName', '')} {config.get('lastName', '')}")

                register_info = {
                    'success': True,
                    'email': config.get('email', ''),
                    'password': config.get('password', ''),
                    'firstName': config.get('firstName', ''),
                    'lastName': config.get('lastName', ''),
                    'masterEmail': config.get('email', ''),
                    'tempmailDomain': config.get('tempmailDomain', 'nnn223.xyz'),
                    'save_warning': False,
                    'message': '注册成功',
                    'logs': logs
                }

                # 检查是否有保存失败的信息
                try:
                    import reset_machine_manual
                    log_content = reset_machine_manual.get_log()
                    if "保存账户信息失败" in log_content:
                        register_info['save_warning'] = True
                        register_info['message'] = '注册成功但账户信息保存失败，请手动保存'
                        logs.append("[自动注册] ⚠️ 账户信息保存失败，请手动保存")
                        if hasattr(self, 'reset_machine_manual'):
                            self.reset_machine_manual.log("[自动注册] ⚠️ 账户信息保存失败，请手动保存")
                except:
                    pass

                logs.append("[自动注册] 🎉 自动注册完成")
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log("[自动注册] 🎉 自动注册完成")

                return register_info
            else:
                error_log = "[自动注册] ❌ 注册流程执行失败"
                logs.append(error_log)
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log(error_log)
                return {'success': False, 'error': '注册失败，请查看日志了解详情', 'logs': logs}

        except Exception as e:
            error_log = f"[自动注册] ❌ 注册异常: {str(e)}"
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(error_log)
            return {"success": False, "error": f"注册失败: {str(e)}", "logs": [error_log]}

    def get_random_config(self):
        """获取随机配置 - 生成随机的邮箱、姓名、密码等信息"""
        try:
            import random
            import string
            import json
            import os

            # 记录开始生成随机配置
            logs = []
            logs.append("[随机配置] 开始生成随机配置信息...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[随机配置] 开始生成随机配置信息...")

            # 生成随机配置
            def generate_random_string(length=8):
                return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

            def generate_random_name():
                first_names = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Riley', 'Avery', 'Quinn', 'Sage', 'River']
                last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez']
                return random.choice(first_names), random.choice(last_names)

            def generate_random_password(length=12):
                chars = string.ascii_letters + string.digits + "!@#$%^&*"
                return ''.join(random.choices(chars, k=length))

            # 生成随机配置
            first_name, last_name = generate_random_name()
            username = generate_random_string(10)
            temp_domain = "nnn223.xyz"  # 使用默认临时邮箱域名
            email = f"{username}@{temp_domain}"
            password = generate_random_password()
            pin_code = f"@{generate_random_string(12)}"

            config_data = {
                'email': email,
                'firstName': first_name,
                'lastName': last_name,
                'password': password,
                'tempDomain': temp_domain,
                'pinCode': pin_code
            }

            # 记录生成的详细配置信息
            logs.append(f"[随机配置] 📧 邮箱地址: {email}")
            logs.append(f"[随机配置] 👤 姓名: {first_name} {last_name}")
            logs.append(f"[随机配置] 🔐 密码: {password}")
            logs.append(f"[随机配置] 📌 PIN码: {pin_code}")
            logs.append(f"[随机配置] 🌐 临时邮箱域名: {temp_domain}")
            logs.append("[随机配置] ✅ 随机配置生成完成")

            if hasattr(self, 'reset_machine_manual'):
                for log_msg in logs[1:]:  # 跳过第一条，因为已经记录了
                    self.reset_machine_manual.log(log_msg)

            return {
                "success": True,
                "logs": logs,  # 添加日志信息到响应中
                **config_data
            }

        except Exception as e:
            # 记录错误信息
            error_log = f"[随机配置] ❌ 生成失败: {str(e)}"
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(error_log)
            return {"success": False, "error": f"生成随机配置失败: {str(e)}", "logs": [error_log]}

    def get_verification_code(self):
        """获取验证码 - 从邮箱获取最新验证码"""
        try:
            import json
            import os
            import sys

            # 记录开始获取验证码
            logs = []
            logs.append("[验证码] 开始获取验证码...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[验证码] 开始获取验证码...")

            # 获取注册配置
            config_file = os.path.join(os.path.expanduser("~"), ".cursor-pro", "register_config.json")
            if not os.path.exists(config_file):
                error_log = f"[验证码] ❌ 配置文件不存在: {config_file}"
                logs.append(error_log)
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log(error_log)
                return {
                    "success": False,
                    "error": "注册配置不存在，请先在设置页面配置注册信息",
                    "logs": logs
                }

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            email = config.get('email', '')
            epin = config.get('tempmailEpin', '')

            logs.append(f"[验证码] 📧 目标邮箱: {email}")
            logs.append(f"[验证码] 🔑 EPIN码: {epin[:4]}****{epin[-4:] if len(epin) > 8 else '****'}")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(f"[验证码] 📧 目标邮箱: {email}")
                self.reset_machine_manual.log(f"[验证码] 🔑 EPIN码: {epin[:4]}****{epin[-4:] if len(epin) > 8 else '****'}")

            if not email:
                error_log = "[验证码] ❌ 配置中没有找到邮箱地址"
                logs.append(error_log)
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log(error_log)
                return {
                    "success": False,
                    "error": "配置中没有找到邮箱地址",
                    "logs": logs
                }

            if not epin:
                error_log = "[验证码] ❌ 配置中没有找到EPIN码"
                logs.append(error_log)
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log(error_log)
                return {
                    "success": False,
                    "error": "配置中没有找到EPIN码，请确保已正确配置临时邮箱信息",
                    "logs": logs
                }

            # 添加email_tabs目录到路径
            email_tabs_path = os.path.join(os.getcwd(), 'email_tabs')
            if email_tabs_path not in sys.path:
                sys.path.insert(0, email_tabs_path)

            logs.append("[验证码] 🔍 正在连接临时邮箱服务...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[验证码] 🔍 正在连接临时邮箱服务...")

            # 导入邮箱接口
            from tempmail_plus_tab import TempMailPlusTab

            # 创建邮箱接口实例
            tempmail = TempMailPlusTab(email, epin)

            logs.append("[验证码] 📬 正在检查Cursor验证邮件...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[验证码] 📬 正在检查Cursor验证邮件...")

            # 先检查是否有Cursor邮件
            if tempmail.check_for_cursor_email():
                logs.append("[验证码] ✅ 找到Cursor验证邮件，正在提取验证码...")
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log("[验证码] ✅ 找到Cursor验证邮件，正在提取验证码...")

                # 获取验证码
                verification_code = tempmail.get_verification_code()

                if verification_code:
                    logs.append(f"[验证码] 🎯 验证码获取成功: {verification_code}")
                    logs.append(f"[验证码] 📧 来源邮箱: {email}")
                    logs.append("[验证码] ✅ 验证码获取完成")
                    if hasattr(self, 'reset_machine_manual'):
                        self.reset_machine_manual.log(f"[验证码] 🎯 验证码获取成功: {verification_code}")
                        self.reset_machine_manual.log(f"[验证码] 📧 来源邮箱: {email}")
                        self.reset_machine_manual.log("[验证码] ✅ 验证码获取完成")

                    return {
                        "success": True,
                        "code": verification_code,
                        "email": email,
                        "subject": "Cursor Verification Code",
                        "time": "刚刚",
                        "logs": logs
                    }
                else:
                    error_log = "[验证码] ❌ 找到Cursor邮件但无法提取验证码"
                    logs.append(error_log)
                    if hasattr(self, 'reset_machine_manual'):
                        self.reset_machine_manual.log(error_log)
                    return {
                        "success": False,
                        "error": "找到Cursor邮件但无法提取验证码",
                        "logs": logs
                    }
            else:
                logs.append("[验证码] ❌ 未找到Cursor验证邮件")
                logs.append("[验证码] 💡 请确保已提交注册申请并等待邮件到达")
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log("[验证码] ❌ 未找到Cursor验证邮件")
                    self.reset_machine_manual.log("[验证码] 💡 请确保已提交注册申请并等待邮件到达")
                return {
                    "success": False,
                    "error": "未找到Cursor验证邮件，请确保已提交注册申请",
                    "logs": logs
                }

        except Exception as e:
            # 记录错误信息
            error_log = f"[验证码] ❌ 获取失败: {str(e)}"
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(error_log)
            return {"success": False, "error": f"获取验证码失败: {str(e)}", "logs": [error_log]}

    def init_cursor(self):
        """初始化Cursor - 模仿原版实现"""
        self._cache.clear()
        self._cache_time.clear()

        try:
            import os
            import shutil
            from datetime import datetime

            # 记录开始初始化
            logs = []
            logs.append("[初始化] 开始初始化Cursor...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[初始化] 开始初始化Cursor...")

            # 获取配置路径
            if os.name == 'nt':  # Windows
                appdata = os.getenv("APPDATA")
                cursor_config_dir = os.path.join(appdata, "Cursor")
                cursor_user_dir = os.path.join(cursor_config_dir, "User")
                logs.append(f"[初始化] 🖥️ 检测到Windows系统")
            else:
                # macOS/Linux
                home = os.path.expanduser("~")
                if hasattr(os, 'uname') and os.uname().sysname == "Darwin":  # macOS
                    cursor_config_dir = os.path.join(home, "Library/Application Support/Cursor")
                    logs.append(f"[初始化] 🍎 检测到macOS系统")
                else:  # Linux
                    cursor_config_dir = os.path.join(home, ".config/cursor")
                    logs.append(f"[初始化] 🐧 检测到Linux系统")
                cursor_user_dir = os.path.join(cursor_config_dir, "User")

            logs.append(f"[初始化] 📁 配置目录: {cursor_config_dir}")
            logs.append(f"[初始化] 👤 用户目录: {cursor_user_dir}")
            if hasattr(self, 'reset_machine_manual'):
                for log_msg in logs[-4:]:  # 记录最后几条日志
                    self.reset_machine_manual.log(log_msg)

            cleanup_results = []

            # 备份并清理用户配置
            if os.path.exists(cursor_user_dir):
                logs.append("[初始化] 📦 发现现有用户配置，开始备份...")
                if hasattr(self, 'reset_machine_manual'):
                    self.reset_machine_manual.log("[初始化] 📦 发现现有用户配置，开始备份...")

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = f"{cursor_user_dir}_backup_{timestamp}"

                try:
                    # 创建备份
                    shutil.copytree(cursor_user_dir, backup_dir)
                    backup_msg = f"✅ 已备份用户配置到: {backup_dir}"
                    cleanup_results.append(backup_msg)
                    logs.append(f"[初始化] {backup_msg}")

                    # 删除原配置
                    shutil.rmtree(cursor_user_dir)
                    clean_msg = f"✅ 已清理用户配置目录: {cursor_user_dir}"
                    cleanup_results.append(clean_msg)
                    logs.append(f"[初始化] {clean_msg}")

                except Exception as e:
                    error_msg = f"❌ 清理用户配置失败: {str(e)}"
                    cleanup_results.append(error_msg)
                    logs.append(f"[初始化] {error_msg}")
            else:
                skip_msg = "ℹ️ 用户配置目录不存在，跳过清理"
                cleanup_results.append(skip_msg)
                logs.append(f"[初始化] {skip_msg}")

            # 重新创建基本目录
            logs.append("[初始化] 🔧 重新创建配置目录...")
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log("[初始化] 🔧 重新创建配置目录...")

            try:
                os.makedirs(cursor_user_dir, exist_ok=True)
                create_msg = f"✅ 已重新创建用户配置目录"
                cleanup_results.append(create_msg)
                logs.append(f"[初始化] {create_msg}")
            except Exception as e:
                error_msg = f"❌ 创建用户配置目录失败: {str(e)}"
                cleanup_results.append(error_msg)
                logs.append(f"[初始化] {error_msg}")

            logs.append("[初始化] 🎉 Cursor初始化完成")
            if hasattr(self, 'reset_machine_manual'):
                # 记录所有操作日志
                for log_msg in logs[4:]:  # 跳过前面已记录的日志
                    self.reset_machine_manual.log(log_msg)

            return {
                "success": True,
                "message": "Cursor初始化完成",
                "details": cleanup_results,
                "logs": logs
            }

        except Exception as e:
            error_log = f"[初始化] ❌ 初始化失败: {str(e)}"
            if hasattr(self, 'reset_machine_manual'):
                self.reset_machine_manual.log(error_log)
            return {"success": False, "error": f"初始化失败: {str(e)}", "logs": [error_log]}
    
    def bypass_version_check(self):
        """版本绕过"""
        try:
            if hasattr(self, 'bypass_version'):
                return self.bypass_version.bypass_version_check()
            else:
                import bypass_version
                return bypass_version.bypass_version_check()
        except Exception as e:
            logger.error(f"版本绕过失败: {e}")
            return {"success": False, "error": str(e)}

    def reset_machine_id(self):
        """重置机器码"""
        try:
            if hasattr(self, 'reset_machine_manual'):
                return self.reset_machine_manual.reset_machine_id()
            else:
                import reset_machine_manual
                return reset_machine_manual.reset_machine_id()
        except Exception as e:
            logger.error(f"重置机器码失败: {e}")
            return {"success": False, "error": str(e)}

    def save_account(self):
        """保存当前账户信息到 cursor_accounts.txt"""
        try:
            # 获取当前账户信息
            account_info = self._get_account_info_impl()
            if 'error' in account_info:
                return {"success": False, "error": "无法获取当前账户信息"}

            import os
            from datetime import datetime

            # 检查是否已存在该邮箱 - 使用新的路径管理系统
            email = account_info.get('email', '未知邮箱')
            if file_exists('cursor_accounts.txt'):
                content = read_file('cursor_accounts.txt')
                if f"Email: {email}" in content:
                    return {
                        "success": False,
                        "error": f"账户 {email} 已存在于列表中"
                    }

            # 准备要保存的账户信息
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 获取真实Token
            real_token = None
            try:
                import cursor_acc_info
                real_token = cursor_acc_info.get_token()
            except Exception as e:
                logger.warning(f"获取真实Token失败: {e}")

            # 构建账户信息文本
            account_text = f"\n{'='*50}\n"
            account_text += f"Email: {email}\n"
            account_text += f"Password: 需要手动获取密码\n"  # 诚实地说明密码状态

            # 诚实地显示Token状态
            if real_token:
                account_text += f"Token: {real_token}\n"
            else:
                account_text += f"Token: 未能获取到有效Token\n"

            account_text += f"Plan: {account_info.get('plan', 'Free')}\n"
            account_text += f"Trial Days: {account_info.get('trial_days', 0)}\n"
            account_text += f"Pro Used: {account_info.get('pro_used', 0)}\n"
            account_text += f"Pro Total: {account_info.get('pro_total', 0)}\n"
            account_text += f"Basic Total: {account_info.get('basic_total', 'No Limit')}\n"
            account_text += f"Saved Time: {timestamp}\n"
            account_text += f"Source: saved\n"
            account_text += f"{'='*50}\n"

            # 追加到账户文件 - 使用新的路径管理系统
            append_file('cursor_accounts.txt', account_text)

            # 清除缓存，确保下次获取时能看到新账户
            cache_key = f"get_registered_accounts"
            if hasattr(self, '_cache') and cache_key in self._cache:
                del self._cache[cache_key]

            return {
                "success": True,
                "message": f"账户 {email} 已保存到账户列表",
                "account_email": email,
                "saved_time": timestamp
            }
        except Exception as e:
            return {"success": False, "error": f"保存账户失败: {e}"}

    def backup_settings(self):
        """备份Cursor设置"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版备份设置模块
            import backup_settings
            from main import translator as main_translator

            # 调用原版备份功能
            result = backup_settings.backup_settings(translator=main_translator)

            if result:
                return {"success": True, "message": "设置备份完成"}
            else:
                return {"success": False, "error": "设置备份失败"}

        except Exception as e:
            return {"success": False, "error": f"备份设置失败: {e}"}

    def backup_session(self):
        """备份会话数据"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版备份会话模块
            import backup_session
            from main import translator as main_translator

            # 调用原版备份功能
            result = backup_session.backup_session(translator=main_translator)

            if result:
                return {"success": True, "message": "会话备份完成"}
            else:
                return {"success": False, "error": "会话备份失败"}

        except Exception as e:
            return {"success": False, "error": f"备份会话失败: {e}"}

    def restore_settings(self):
        """恢复设置"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版恢复设置模块
            import restore_settings
            from main import translator as main_translator

            # 调用原版恢复功能
            result = restore_settings.restore_settings(translator=main_translator, auto_confirm=True)

            if result:
                return {"success": True, "message": "设置恢复完成"}
            else:
                return {"success": False, "error": "设置恢复失败"}

        except Exception as e:
            return {"success": False, "error": f"恢复设置失败: {e}"}

    def restore_session(self):
        """恢复会话"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版恢复会话模块
            import restore_session
            from main import translator as main_translator

            # 调用原版恢复功能
            result = restore_session.restore_session(translator=main_translator, auto_confirm=True)

            if result:
                return {"success": True, "message": "会话恢复完成"}
            else:
                return {"success": False, "error": "会话恢复失败"}

        except Exception as e:
            return {"success": False, "error": f"恢复会话失败: {e}"}

    def restart_cursor(self):
        """重启Cursor"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版重启模块
            import quit_cursor
            from main import translator as main_translator
            import time
            import subprocess

            # 关闭Cursor
            quit_result = quit_cursor.quit_cursor(main_translator, timeout=10)
            if quit_result:
                logger.info("✅ Cursor已关闭")
            else:
                logger.warning("⚠️ Cursor关闭可能不完整")

            # 等待一下确保进程完全关闭
            time.sleep(3)

            # 启动Cursor
            if os.name == 'nt':  # Windows
                possible_paths = [
                    os.path.join(os.getenv('LOCALAPPDATA', ''), 'Programs', 'Cursor', 'Cursor.exe'),
                    os.path.join(os.getenv('PROGRAMFILES', ''), 'Cursor', 'Cursor.exe'),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        subprocess.Popen([path], shell=False)
                        return {"success": True, "message": "Cursor重启完成"}

                return {"success": False, "error": "未找到Cursor可执行文件"}
            else:
                subprocess.Popen(['cursor'], shell=False)
                return {"success": True, "message": "Cursor重启完成"}

        except Exception as e:
            return {"success": False, "error": f"重启Cursor失败: {e}"}

    def list_settings_backups(self):
        """列出设置备份"""
        try:
            # 直接导入本地的备份设置模块
            import backup_settings
            from main import translator as main_translator

            # 调用列出备份功能
            backups = backup_settings.list_backups(translator=main_translator)

            # 过滤出设置类型的备份
            settings_backups = []
            for backup in backups:
                if 'settings' in backup.get('name', '').lower():
                    settings_backups.append({
                        'id': backup.get('name', ''),
                        'name': backup.get('name', ''),
                        'type': 'settings',
                        'size': backup.get('files', 0) * 1024,  # 估算大小
                        'createdAt': backup.get('timestamp', ''),
                        'description': '自动创建的设置备份'
                    })

            return settings_backups

        except Exception as e:
            return []

    def list_session_backups(self):
        """列出会话备份"""
        try:
            # 直接导入本地的备份会话模块
            import backup_session
            from main import translator as main_translator

            # 调用列出备份功能
            backups = backup_session.list_session_backups(translator=main_translator)

            # 转换为前端需要的格式
            session_backups = []
            for backup in backups:
                session_backups.append({
                    'id': backup.get('name', ''),
                    'name': backup.get('name', ''),
                    'type': 'session',
                    'size': backup.get('size', 0),
                    'createdAt': backup.get('timestamp', ''),
                    'description': '对话会话备份'
                })

            return session_backups

        except Exception as e:
            return []

    def delete_backup(self, backup_name=None):
        """删除备份"""
        try:
            # 添加原版目录到路径
            import sys
            import os
            original_path = os.path.join(os.getcwd(), '原版', 'cursor-pro-main')
            if original_path not in sys.path:
                sys.path.insert(0, original_path)

            # 导入原版删除备份模块
            import delete_backup
            from main import translator as main_translator

            # 调用原版删除备份功能
            result = delete_backup.delete_backup(backup_name, translator=main_translator)

            if result:
                return {"success": True, "message": f"备份 {backup_name} 删除成功"}
            else:
                return {"success": False, "error": f"删除备份 {backup_name} 失败"}

        except Exception as e:
            return {"success": False, "error": f"删除备份失败: {e}"}

    def get_account_list(self):
        """获取账户列表"""
        try:
            import os
            import json
            from datetime import datetime

            # 读取账户文件 - 使用新的路径管理系统
            accounts = []
            content = read_file('cursor_accounts.txt')
            if content:

                # 解析账户信息
                account_blocks = content.split('=' * 50)
                for block in account_blocks:
                    if 'Email:' in block:
                        lines = block.strip().split('\n')
                        account = {}

                        for line in lines:
                            if line.startswith('Email:'):
                                account['email'] = line.replace('Email:', '').strip()
                            elif line.startswith('Password:'):
                                account['password'] = line.replace('Password:', '').strip()
                            elif line.startswith('Token:'):
                                account['token'] = line.replace('Token:', '').strip()
                            elif line.startswith('Usage Limit:'):
                                account['usage_limit'] = line.replace('Usage Limit:', '').strip()

                        if account.get('email'):
                            account['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            account['status'] = 'active' if account.get('token') else 'inactive'
                            accounts.append(account)

            return {"success": True, "data": accounts}

        except Exception as e:
            return {"success": False, "error": f"获取账户列表失败: {e}"}

    def delete_account(self, email=None):
        """删除账户"""
        try:
            import os
            from datetime import datetime

            if not email:
                return {"success": False, "error": "邮箱地址不能为空"}

            # 使用新的路径管理系统
            if not file_exists('cursor_accounts.txt'):
                return {"success": False, "error": "账户文件不存在"}

            # 读取现有账户
            content = read_file('cursor_accounts.txt')

            # 分割账户块
            account_blocks = content.split('=' * 50)
            new_blocks = []
            deleted_block = None
            deleted = False

            for block in account_blocks:
                if 'Email:' in block:
                    # 检查是否是要删除的账户
                    lines = block.strip().split('\n')
                    block_email = None

                    for line in lines:
                        if line.startswith('Email:'):
                            block_email = line.replace('Email:', '').strip()
                            break

                    if block_email != email:
                        new_blocks.append('=' * 50)
                        new_blocks.append(block.strip())
                        new_blocks.append('=' * 50)
                        new_blocks.append('')
                    else:
                        # 保存被删除的账户块
                        deleted_block = '=' * 50 + '\n' + block.strip() + '\n' + '=' * 50 + '\n\n'
                        deleted = True
                elif block.strip():
                    new_blocks.append(block)

            if not deleted:
                return {"success": False, "error": f"未找到邮箱为 {email} 的账户"}

            # 将被删除的账户追加到备份文件
            if deleted_block:
                # 确保备份文件存在
                if not file_exists('cursor_accounts_backup.txt'):
                    write_file('cursor_accounts_backup.txt', "# Cursor 账号备份文件\n# 此文件用于存储被删除的账号，方便恢复\n# 格式与 cursor_accounts.txt 相同\n\n")

                # 追加被删除的账户到备份文件
                append_file('cursor_accounts_backup.txt', deleted_block)

            # 写入新内容到主文件
            write_file('cursor_accounts.txt', '\n'.join(new_blocks))

            return {"success": True, "message": f"账户 {email} 删除成功，已备份到 {backup_file}"}

        except Exception as e:
            return {"success": False, "error": f"删除账户失败: {e}"}

    def switch_account(self, email=None, password=None):
        """切换账户"""
        try:
            import os
            import json
            import shutil
            import sys
            import sqlite3
            import psutil
            from datetime import datetime

            if not email:
                return {"success": False, "error": "邮箱地址不能为空"}

            # 自动处理Cursor进程
            cursor_processes = self._check_cursor_running()
            if cursor_processes:
                print(f"🔄 检测到 {len(cursor_processes)} 个Cursor进程正在运行")
                print("🔄 正在自动关闭Cursor进程...")

                close_result = self._auto_close_cursor(cursor_processes)
                if not close_result["success"]:
                    return {
                        "success": False,
                        "error": f"无法自动关闭Cursor进程: {close_result['error']}",
                        "suggestion": "请手动关闭Cursor后重试"
                    }

                print("✅ Cursor进程已关闭，等待3秒确保完全退出...")
                import time
                time.sleep(3)

            # 使用新的路径管理系统
            if not file_exists('cursor_accounts.txt'):
                return {"success": False, "error": "账户文件不存在，请先保存一些账户"}

            # 读取账户文件
            content = read_file('cursor_accounts.txt')

            # 查找指定邮箱的账户信息
            account_blocks = content.split('=' * 50)
            target_account = None
            account_data = {}

            for block in account_blocks:
                if 'Email:' in block:
                    lines = block.strip().split('\n')
                    block_email = None

                    for line in lines:
                        if line.startswith('Email:'):
                            block_email = line.replace('Email:', '').strip()
                            break

                    if block_email == email:
                        target_account = block.strip()
                        break

            if not target_account:
                return {"success": False, "error": f"未找到邮箱为 {email} 的账户信息"}

            # 解析账户信息
            lines = target_account.split('\n')
            machine_ids = {}
            current_section = None

            for line in lines:
                line = line.strip()
                if ':' in line and not line.startswith('  '):
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()

                    if key == 'Email':
                        account_data['email'] = value
                    elif key == 'Password':
                        account_data['password'] = value
                    elif key == 'Token':
                        account_data['token'] = value
                    elif key == 'Usage Limit':
                        account_data['usage_limit'] = value
                    elif key == 'Machine IDs':
                        current_section = 'machine_ids'
                elif line.startswith('  ') and current_section == 'machine_ids':
                    # 解析机器ID
                    if ':' in line:
                        key, value = line.strip().split(':', 1)
                        machine_ids[key.strip()] = value.strip()

            account_data['machine_ids'] = machine_ids

            # 获取Cursor配置路径
            cursor_paths = self._get_cursor_paths()
            if not cursor_paths:
                return {"success": False, "error": "无法获取Cursor配置路径"}

            # 应用新的账户配置
            print(f"🔧 开始应用账户配置...")
            print(f"📊 账户数据: {account_data}")
            print(f"📁 Cursor路径: {cursor_paths}")

            switch_result = self._apply_account_config(account_data, cursor_paths)
            print(f"⚙️ 配置应用结果: {switch_result}")

            if switch_result['success']:
                # 验证配置是否真的写入成功
                print("🔍 验证配置文件写入...")
                verification_result = self._verify_account_switch(email, cursor_paths)
                print(f"✅ 验证结果: {verification_result}")

                # 自动重启Cursor
                print("🔄 正在自动重启Cursor...")
                restart_result = self._auto_restart_cursor()
                print(f"🚀 重启结果: {restart_result}")

                details = switch_result['details'].copy()
                details.extend(verification_result.get('details', []))

                if restart_result['success']:
                    details.extend(restart_result['details'])

                    # 等待Cursor启动后再次验证
                    print("⏳ 等待Cursor完全启动...")
                    import time
                    time.sleep(5)

                    # 最终验证
                    final_check = self._final_account_verification(email)
                    print(f"🎯 最终验证: {final_check}")
                    details.extend(final_check.get('details', []))

                    return {
                        "success": True,
                        "message": f"成功切换到账户: {email}，Cursor已自动重启",
                        "details": details,
                        "account_email": email,
                        "auto_restart": True,
                        "restart_success": True,
                        "verification": verification_result,
                        "final_check": final_check
                    }
                else:
                    details.append(f"⚠️ 自动重启失败: {restart_result['error']}")
                    details.append("💡 请手动启动Cursor")
                    return {
                        "success": True,
                        "message": f"成功切换到账户: {email}，但需要手动重启Cursor",
                        "details": details,
                        "account_email": email,
                        "auto_restart": True,
                        "restart_success": False,
                        "restart_error": restart_result['error'],
                        "verification": verification_result
                    }
            else:
                print(f"❌ 配置应用失败: {switch_result['error']}")
                return {"success": False, "error": switch_result['error']}

        except Exception as e:
            return {"success": False, "error": f"切换账户失败: {e}"}

    def _get_cursor_paths(self):
        """获取Cursor配置文件路径"""
        try:
            import os
            import sys

            if sys.platform == "win32":
                appdata = os.getenv('APPDATA', '')
                if not appdata:
                    return None
                cursor_dir = os.path.join(appdata, 'Cursor')
            elif sys.platform == 'darwin':  # macOS
                home = os.path.expanduser("~")
                cursor_dir = os.path.join(home, "Library/Application Support/Cursor")
            else:  # Linux
                home = os.path.expanduser("~")
                cursor_dir = os.path.join(home, ".config/cursor")

            if not os.path.exists(cursor_dir):
                return None

            return {
                'cursor_dir': cursor_dir,
                'storage_json': os.path.join(cursor_dir, 'User', 'globalStorage', 'storage.json'),
                'state_vscdb': os.path.join(cursor_dir, 'User', 'globalStorage', 'state.vscdb'),
                'machine_id': os.path.join(cursor_dir, 'machineId')
            }
        except Exception:
            return None

    def _check_cursor_running(self):
        """检查Cursor是否正在运行（排除Vue界面）"""
        try:
            import psutil
            import os
            cursor_processes = []

            # 获取当前工作目录，用于排除Vue界面
            current_dir = os.getcwd()

            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cwd']):
                try:
                    proc_name = proc.info['name'].lower()
                    proc_exe = proc.info['exe']
                    proc_cwd = proc.info.get('cwd', '')

                    # 检查是否是Cursor相关进程
                    if ('cursor' in proc_name or
                        (proc_exe and 'cursor' in proc_exe.lower())):

                        # 排除Vue界面的Electron进程
                        if (proc_name == 'electron.exe' and
                            proc_cwd and current_dir in proc_cwd):
                            continue  # 跳过Vue界面进程

                        # 排除当前Python进程
                        if 'python' in proc_name.lower():
                            continue

                        cursor_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'exe': proc_exe,
                            'cwd': proc_cwd
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return cursor_processes if cursor_processes else None
        except ImportError:
            # 如果没有psutil，跳过检查
            return None
        except Exception:
            return None

    def _auto_close_cursor(self, cursor_processes):
        """自动关闭Cursor进程"""
        try:
            import psutil
            import time

            closed_processes = []
            failed_processes = []

            # 首先尝试优雅关闭
            for proc_info in cursor_processes:
                try:
                    proc = psutil.Process(proc_info['pid'])
                    proc.terminate()  # 发送SIGTERM信号
                    closed_processes.append(f"✅ 优雅关闭进程: {proc_info['name']} (PID: {proc_info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    failed_processes.append(f"❌ 无法关闭进程: {proc_info['name']} (PID: {proc_info['pid']}) - {e}")

            # 等待进程关闭
            time.sleep(2)

            # 检查是否还有进程存活，强制关闭
            remaining_processes = self._check_cursor_running()
            if remaining_processes:
                for proc_info in remaining_processes:
                    try:
                        proc = psutil.Process(proc_info['pid'])
                        proc.kill()  # 强制关闭
                        closed_processes.append(f"🔥 强制关闭进程: {proc_info['name']} (PID: {proc_info['pid']})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        failed_processes.append(f"❌ 无法强制关闭进程: {proc_info['name']} (PID: {proc_info['pid']}) - {e}")

            # 最终检查
            final_check = self._check_cursor_running()
            if final_check:
                return {
                    "success": False,
                    "error": f"仍有 {len(final_check)} 个进程无法关闭",
                    "details": closed_processes + failed_processes
                }
            else:
                return {
                    "success": True,
                    "message": "所有Cursor进程已成功关闭",
                    "details": closed_processes
                }

        except ImportError:
            return {"success": False, "error": "缺少psutil模块，无法自动关闭进程"}
        except Exception as e:
            return {"success": False, "error": f"关闭进程时出错: {e}"}

    def _auto_restart_cursor(self):
        """自动重启Cursor"""
        try:
            import subprocess
            import os
            import time

            # 常见的Cursor安装路径
            possible_paths = [
                "D:\\cursor\\Cursor.exe",  # 从之前的进程信息看到的路径
                os.path.join(os.getenv('LOCALAPPDATA', ''), 'Programs', 'Cursor', 'Cursor.exe'),
                os.path.join(os.getenv('PROGRAMFILES', ''), 'Cursor', 'Cursor.exe'),
                os.path.join(os.getenv('PROGRAMFILES(X86)', ''), 'Cursor', 'Cursor.exe'),
            ]

            cursor_exe = None
            for path in possible_paths:
                if os.path.exists(path):
                    cursor_exe = path
                    break

            if not cursor_exe:
                return {
                    "success": False,
                    "error": "未找到Cursor可执行文件",
                    "details": [f"❌ 检查路径: {path}" for path in possible_paths]
                }

            # 启动Cursor
            try:
                subprocess.Popen([cursor_exe], shell=False)
                time.sleep(2)  # 等待启动

                # 验证是否启动成功
                new_processes = self._check_cursor_running()
                if new_processes:
                    return {
                        "success": True,
                        "message": f"Cursor已成功重启 (发现 {len(new_processes)} 个进程)",
                        "details": [
                            f"✅ 使用路径: {cursor_exe}",
                            f"✅ 启动了 {len(new_processes)} 个进程"
                        ],
                        "cursor_path": cursor_exe
                    }
                else:
                    return {
                        "success": False,
                        "error": "Cursor启动后未检测到进程",
                        "details": [f"⚠️ 使用路径: {cursor_exe}"]
                    }

            except Exception as e:
                return {
                    "success": False,
                    "error": f"启动Cursor失败: {e}",
                    "details": [f"❌ 尝试路径: {cursor_exe}"]
                }

        except Exception as e:
            return {"success": False, "error": f"重启功能出错: {e}"}

    def _verify_account_switch(self, target_email, cursor_paths):
        """验证账户切换是否成功写入配置文件"""
        try:
            import os
            import json
            import sqlite3

            verification_details = []
            success_count = 0
            total_checks = 0

            # 1. 检查storage.json
            if os.path.exists(cursor_paths['storage_json']):
                total_checks += 1
                try:
                    with open(cursor_paths['storage_json'], 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    auth_data = storage_data.get('workbench.accounts.auth', {})
                    user_data = storage_data.get('workbench.accounts.user', {})

                    if auth_data.get('accessToken'):
                        verification_details.append("✅ storage.json: 找到accessToken")
                        success_count += 1
                    else:
                        verification_details.append("❌ storage.json: 未找到accessToken")

                    if user_data.get('email') == target_email:
                        verification_details.append(f"✅ storage.json: 邮箱已更新为 {target_email}")
                        success_count += 1
                    else:
                        verification_details.append(f"❌ storage.json: 邮箱仍为 {user_data.get('email', '未知')}")

                except Exception as e:
                    verification_details.append(f"❌ storage.json: 读取失败 - {e}")
            else:
                verification_details.append("❌ storage.json: 文件不存在")

            # 2. 检查state.vscdb
            if os.path.exists(cursor_paths['state_vscdb']):
                total_checks += 1
                try:
                    conn = sqlite3.connect(cursor_paths['state_vscdb'])
                    cursor = conn.cursor()

                    # 检查认证信息
                    cursor.execute("SELECT value FROM ItemTable WHERE key = 'workbench.accounts.auth'")
                    auth_row = cursor.fetchone()

                    if auth_row:
                        auth_data = json.loads(auth_row[0])
                        if auth_data.get('accessToken'):
                            verification_details.append("✅ state.vscdb: 找到accessToken")
                            success_count += 1
                        else:
                            verification_details.append("❌ state.vscdb: 未找到accessToken")
                    else:
                        verification_details.append("❌ state.vscdb: 未找到认证记录")

                    # 检查用户信息
                    cursor.execute("SELECT value FROM ItemTable WHERE key = 'workbench.accounts.user'")
                    user_row = cursor.fetchone()

                    if user_row:
                        user_data = json.loads(user_row[0])
                        if user_data.get('email') == target_email:
                            verification_details.append(f"✅ state.vscdb: 邮箱已更新为 {target_email}")
                            success_count += 1
                        else:
                            verification_details.append(f"❌ state.vscdb: 邮箱仍为 {user_data.get('email', '未知')}")
                    else:
                        verification_details.append("❌ state.vscdb: 未找到用户记录")

                    conn.close()
                except Exception as e:
                    verification_details.append(f"❌ state.vscdb: 检查失败 - {e}")
            else:
                verification_details.append("❌ state.vscdb: 文件不存在")

            # 3. 检查machineId
            if cursor_paths.get('machine_id') and os.path.exists(cursor_paths['machine_id']):
                total_checks += 1
                try:
                    with open(cursor_paths['machine_id'], 'r', encoding='utf-8') as f:
                        machine_id = f.read().strip()
                    if machine_id:
                        verification_details.append(f"✅ machineId: 已更新 ({machine_id[:20]}...)")
                        success_count += 1
                    else:
                        verification_details.append("❌ machineId: 文件为空")
                except Exception as e:
                    verification_details.append(f"❌ machineId: 读取失败 - {e}")
            else:
                verification_details.append("❌ machineId: 文件不存在")

            success_rate = (success_count / max(total_checks, 1)) * 100
            verification_details.append(f"📊 验证成功率: {success_count}/{total_checks} ({success_rate:.1f}%)")

            return {
                "success": success_count > 0,
                "success_rate": success_rate,
                "details": verification_details,
                "success_count": success_count,
                "total_checks": total_checks
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"验证过程出错: {e}",
                "details": [f"❌ 验证失败: {e}"]
            }

    def _final_account_verification(self, target_email):
        """最终验证当前登录的账户"""
        try:
            verification_details = []

            # 使用现有的get_account_info方法
            current_account = self.get_account_info()

            if current_account and current_account.get('email'):
                current_email = current_account['email']
                if current_email == target_email:
                    verification_details.append(f"✅ 最终验证成功: 当前登录账户为 {current_email}")
                    verification_details.append(f"📊 账户信息: {current_account}")
                    return {
                        "success": True,
                        "current_email": current_email,
                        "target_email": target_email,
                        "account_info": current_account,
                        "details": verification_details
                    }
                else:
                    verification_details.append(f"❌ 最终验证失败: 当前账户仍为 {current_email}，目标账户 {target_email}")
                    verification_details.append(f"📊 当前账户信息: {current_account}")
                    return {
                        "success": False,
                        "current_email": current_email,
                        "target_email": target_email,
                        "account_info": current_account,
                        "details": verification_details
                    }
            else:
                verification_details.append("❌ 无法获取当前账户信息")
                return {
                    "success": False,
                    "error": "无法获取当前账户信息",
                    "details": verification_details
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"最终验证出错: {e}",
                "details": [f"❌ 最终验证失败: {e}"]
            }

    def _apply_account_config(self, account_data, cursor_paths):
        """应用账户配置到Cursor"""
        try:
            import os
            import json
            import sqlite3
            import uuid

            results = []

            # 1. 更新storage.json文件
            token = account_data.get('token')

            # 检查Token是否有效
            if token and token in ['已设置', '未能获取到有效Token', '需要手动获取Token']:
                details.append(f"❌ Token无效: {token}")
                return {
                    "success": False,
                    "error": f"账户Token无效: {token}",
                    "details": details,
                    "suggestion": "请重新保存该账户以获取有效Token，或手动登录该账户"
                }

            if token and os.path.exists(cursor_paths['storage_json']):
                try:
                    with open(cursor_paths['storage_json'], 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 更新认证token
                    if 'workbench.accounts.auth' not in storage_data:
                        storage_data['workbench.accounts.auth'] = {}

                    storage_data['workbench.accounts.auth'] = {
                        "accessToken": token,
                        "refreshToken": account_data.get('refresh_token', ''),
                        "expiresAt": "2025-12-31T23:59:59.999Z"  # 设置一个未来的过期时间
                    }

                    # 更新用户信息
                    if account_data.get('email'):
                        storage_data['workbench.accounts.user'] = {
                            "email": account_data['email'],
                            "id": str(uuid.uuid4()),
                            "name": account_data['email'].split('@')[0]
                        }

                    with open(cursor_paths['storage_json'], 'w', encoding='utf-8') as f:
                        json.dump(storage_data, f, indent=2)

                    results.append("✅ 已更新storage.json")
                except Exception as e:
                    results.append(f"❌ 更新storage.json失败: {e}")

            # 2. 更新state.vscdb数据库
            if os.path.exists(cursor_paths['state_vscdb']):
                try:
                    conn = sqlite3.connect(cursor_paths['state_vscdb'])
                    cursor = conn.cursor()

                    # 更新认证信息
                    if account_data.get('token'):
                        cursor.execute("""
                            INSERT OR REPLACE INTO ItemTable (key, value)
                            VALUES ('workbench.accounts.auth', ?)
                        """, (json.dumps({
                            "accessToken": account_data['token'],
                            "refreshToken": account_data.get('refresh_token', ''),
                            "expiresAt": "2025-12-31T23:59:59.999Z"
                        }),))

                    # 更新用户信息
                    if account_data.get('email'):
                        cursor.execute("""
                            INSERT OR REPLACE INTO ItemTable (key, value)
                            VALUES ('workbench.accounts.user', ?)
                        """, (json.dumps({
                            "email": account_data['email'],
                            "id": str(uuid.uuid4()),
                            "name": account_data['email'].split('@')[0]
                        }),))

                    conn.commit()
                    conn.close()
                    results.append("✅ 已更新state.vscdb")
                except Exception as e:
                    results.append(f"❌ 更新state.vscdb失败: {e}")

            # 3. 更新机器ID
            if account_data.get('machine_ids') and cursor_paths['machine_id']:
                try:
                    # 从保存的机器ID中选择一个合适的
                    machine_ids = account_data['machine_ids']
                    selected_machine_id = None

                    # 优先选择machineId
                    if 'machineId' in machine_ids:
                        selected_machine_id = machine_ids['machineId']
                    elif 'machine_id' in machine_ids:
                        selected_machine_id = machine_ids['machine_id']
                    elif machine_ids:
                        # 选择第一个可用的机器ID
                        selected_machine_id = list(machine_ids.values())[0]

                    if selected_machine_id:
                        os.makedirs(os.path.dirname(cursor_paths['machine_id']), exist_ok=True)
                        with open(cursor_paths['machine_id'], 'w', encoding='utf-8') as f:
                            f.write(selected_machine_id)
                        results.append("✅ 已更新machineId")
                    else:
                        results.append("⚠️ 未找到可用的机器ID")
                except Exception as e:
                    results.append(f"❌ 更新machineId失败: {e}")

            return {
                "success": True,
                "details": results
            }

        except Exception as e:
            return {"success": False, "error": f"应用配置失败: {e}"}

    def get_config_files(self):
        """获取配置文件列表"""
        try:
            import os
            import json
            import glob
            from datetime import datetime

            config_files = []

            def add_file_if_exists(file_path, name, file_type):
                """添加文件到列表（如果存在）"""
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    config_files.append({
                        "name": name,
                        "path": file_path,
                        "size": f"{stat.st_size} bytes",
                        "modified": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
                        "type": file_type
                    })

            # 1. 注册配置文件
            register_config_path = os.path.join(os.path.expanduser("~"), ".cursor-pro", "register_config.json")
            add_file_if_exists(register_config_path, "register_config.json", "register")

            # 2. 账户数据文件
            accounts_file = "cursor_accounts.txt"
            add_file_if_exists(accounts_file, "cursor_accounts.txt", "accounts")

            # 3. 账户备份文件
            backup_file = "cursor_accounts_backup.txt"
            add_file_if_exists(backup_file, "cursor_accounts_backup.txt", "backup")

            # 4. 配置文件
            config_file = "config.ini"
            add_file_if_exists(config_file, "config.ini", "config")

            # 5. 不再扫描调试日志文件（没什么用）

            # 6. Cursor配置文件
            appdata = os.getenv('APPDATA', '')
            if appdata:
                cursor_files = [
                    (os.path.join(appdata, 'Cursor', 'User', 'globalStorage', 'storage.json'), "storage.json"),
                    (os.path.join(appdata, 'Cursor', 'User', 'globalStorage', 'state.vscdb'), "state.vscdb"),
                    (os.path.join(appdata, 'Cursor', 'User', 'globalStorage', 'state.vscdb.new'), "state.vscdb.new"),
                    (os.path.join(appdata, 'Cursor', 'machineId'), "machineId"),
                ]

                for file_path, name in cursor_files:
                    add_file_if_exists(file_path, name, "cursor")

            # 7. 备份目录
            backup_dirs = [
                os.path.join(os.path.expanduser("~"), "Documents", ".cursor-pro", "backups"),
                os.path.join(os.path.expanduser("~"), "Documents", ".cursor-pro", "session_backups")
            ]

            for backup_dir in backup_dirs:
                if os.path.exists(backup_dir):
                    try:
                        backup_count = len([d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d))])
                        config_files.append({
                            "name": os.path.basename(backup_dir),
                            "path": backup_dir,
                            "size": f"{backup_count} 个备份",
                            "modified": datetime.fromtimestamp(os.path.getmtime(backup_dir)).strftime("%Y-%m-%d %H:%M:%S"),
                            "type": "backup_dir"
                        })
                    except:
                        pass

            return {"success": True, "data": config_files}

        except Exception as e:
            return {"success": False, "error": f"获取配置文件列表失败: {e}"}

    def get_cursor_version(self):
        """获取Cursor版本"""
        try:
            import os
            import json
            import platform

            # 根据操作系统确定Cursor路径
            if platform.system() == "Windows":
                # Windows路径
                possible_paths = [
                    os.path.join(os.getenv('LOCALAPPDATA', ''), 'Programs', 'Cursor', 'resources', 'app', 'package.json'),
                    os.path.join(os.getenv('PROGRAMFILES', ''), 'Cursor', 'resources', 'app', 'package.json'),
                ]
            elif platform.system() == "Darwin":  # macOS
                possible_paths = [
                    "/Applications/Cursor.app/Contents/Resources/app/package.json",
                ]
            else:  # Linux
                possible_paths = [
                    "/opt/cursor/resources/app/package.json",
                    os.path.expanduser("~/cursor/resources/app/package.json"),
                ]

            # 查找package.json文件
            package_json_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    package_json_path = path
                    break

            if not package_json_path:
                return {"success": False, "error": "未找到Cursor安装路径"}

            # 读取package.json获取版本
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)

            version = package_data.get('version', '未知')
            name = package_data.get('name', 'Cursor')

            return {
                "success": True,
                "data": {
                    "version": version,
                    "name": name,
                    "path": package_json_path,
                    "display_version": f"Cursor v{version}"
                }
            }

        except Exception as e:
            return {"success": False, "error": f"获取Cursor版本失败: {e}"}

    def toggle_auto_update(self):
        """切换自动更新设置（禁用自动更新）"""
        try:
            import disable_auto_update

            # 创建禁用器实例
            disabler = disable_auto_update.AutoUpdateDisabler()

            # 执行禁用操作
            result = disabler.disable_auto_update()

            if result:
                return "✅ Cursor自动更新已成功禁用！\n\n🔒 已执行以下操作：\n• 结束Cursor进程\n• 删除更新程序目录\n• 清空更新配置文件\n• 创建阻止文件\n• 移除更新URL\n\n💡 现在Cursor将不会自动更新"
            else:
                return "❌ 禁用自动更新失败，请检查权限或手动操作"

        except Exception as e:
            return f"❌ 禁用自动更新失败: {str(e)}"

    def get_system_info(self):
        """获取系统信息"""
        try:
            import platform
            import os
            from pathlib import Path

            system_info = {
                "os": platform.system(),
                "platform": platform.platform(),
                "arch": platform.machine(),
                "python_version": platform.python_version(),
                "homeDir": str(Path.home()),
                "appDataDir": os.path.expandvars("%APPDATA%") if platform.system() == "Windows" else str(Path.home()),
                "documentsDir": str(Path.home() / "Documents"),
                "tempDir": os.path.expandvars("%TEMP%") if platform.system() == "Windows" else "/tmp"
            }

            return {"success": True, "data": system_info}

        except Exception as e:
            return {"success": False, "error": f"获取系统信息失败: {e}"}

    def detect_cursor_paths(self):
        """检测Cursor相关路径"""
        try:
            import platform
            import os
            from pathlib import Path

            system = platform.system()
            home = Path.home()

            if system == "Windows":
                appdata = Path(os.path.expandvars("%APPDATA%"))
                paths = {
                    "cursorDataDir": str(appdata / "Cursor"),
                    "cursorConfigDir": str(appdata / "Cursor" / "User"),
                    "userDataDir": str(appdata / "Cursor" / "User"),
                    "globalStorageDir": str(appdata / "Cursor" / "User" / "globalStorage"),
                    "workspaceStorageDir": str(appdata / "Cursor" / "User" / "workspaceStorage"),
                    "extensionsDir": str(home / ".cursor" / "extensions"),
                    "logsDir": str(appdata / "Cursor" / "logs")
                }
            elif system == "Darwin":  # macOS
                app_support = home / "Library" / "Application Support"
                paths = {
                    "cursorDataDir": str(app_support / "Cursor"),
                    "cursorConfigDir": str(app_support / "Cursor" / "User"),
                    "userDataDir": str(app_support / "Cursor" / "User"),
                    "globalStorageDir": str(app_support / "Cursor" / "User" / "globalStorage"),
                    "workspaceStorageDir": str(app_support / "Cursor" / "User" / "workspaceStorage"),
                    "extensionsDir": str(home / ".cursor" / "extensions"),
                    "logsDir": str(home / "Library" / "Logs" / "Cursor")
                }
            else:  # Linux
                config_dir = home / ".config"
                paths = {
                    "cursorDataDir": str(config_dir / "Cursor"),
                    "cursorConfigDir": str(config_dir / "Cursor" / "User"),
                    "userDataDir": str(config_dir / "Cursor" / "User"),
                    "globalStorageDir": str(config_dir / "Cursor" / "User" / "globalStorage"),
                    "workspaceStorageDir": str(config_dir / "Cursor" / "User" / "workspaceStorage"),
                    "extensionsDir": str(home / ".cursor" / "extensions"),
                    "logsDir": str(config_dir / "Cursor" / "logs")
                }

            return {"success": True, "data": paths}

        except Exception as e:
            return {"success": False, "error": f"检测Cursor路径失败: {e}"}



    def _save_cursor_accounts(self, accounts_data):
        """保存Cursor账户数据"""
        try:
            import json

            # 使用新的路径管理系统保存到cursor_accounts.txt文件
            if isinstance(accounts_data, list):
                # 如果是账户列表，转换为文本格式
                content = ""
                for account in accounts_data:
                    if isinstance(account, dict):
                        email = account.get('email', '')
                        password = account.get('password', '')
                        token = account.get('token', '')
                        content += f"{email}:{password}:{token}\n"
                    else:
                        content += f"{account}\n"
                write_file('cursor_accounts.txt', content)
            else:
                # 如果是其他格式，直接保存为JSON
                import json
                content = json.dumps(accounts_data, ensure_ascii=False, indent=2)
                write_file('cursor_accounts.txt', content)

            return {"success": True, "message": "账户数据保存成功"}

        except Exception as e:
            logger.error(f"保存账户数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _save_app_config(self, config_data):
        """保存应用配置"""
        try:
            import json
            import os

            # 保存到配置文件
            config_file = "app_config.json"

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            return {"success": True, "message": "应用配置保存成功"}

        except Exception as e:
            logger.error(f"保存应用配置失败: {e}")
            return {"success": False, "error": str(e)}

    def _save_cursor_account_info(self, account_info):
        """保存Cursor账户信息"""
        try:
            import json

            # 保存到账户信息文件 - 使用统一路径管理
            write_file('cursor_account_info.json', json.dumps(account_info, ensure_ascii=False, indent=2))

            return {"success": True, "message": "账户信息保存成功"}

        except Exception as e:
            logger.error(f"保存账户信息失败: {e}")
            return {"success": False, "error": str(e)}

    def scan_config_files(self):
        """扫描配置文件"""
        try:
            import os
            import glob
            from pathlib import Path
            from datetime import datetime

            files = []

            # 使用新的数据路径管理系统
            try:
                from data_path_manager import get_data_path_manager
                path_manager = get_data_path_manager()

                # 扫描整个cursor-pro目录及其所有子目录
                base_dir = str(path_manager.get_path('base'))  # D:\新Roaming\cursor-pro

                scan_patterns = [
                    # data目录
                    (str(path_manager.get_path('data')), "*.txt", "data"),
                    (str(path_manager.get_path('data')), "*.db", "data"),
                    (str(path_manager.get_path('data')), "*.sqlite", "data"),
                    (str(path_manager.get_path('data')), "*.vscdb", "data"),
                    (str(path_manager.get_path('data')), "*.json", "data"),

                    # config目录
                    (str(path_manager.get_path('config')), "*.ini", "config"),
                    (str(path_manager.get_path('config')), "*.json", "config"),
                    (str(path_manager.get_path('config')), "*.txt", "config"),
                    (str(path_manager.get_path('config')), "*.cfg", "config"),

                    # logs目录
                    (str(path_manager.get_path('logs')), "*.log", "logs"),
                    (str(path_manager.get_path('logs')), "*.txt", "logs"),

                    # backups目录 (数据迁移备份)
                    (str(path_manager.get_path('backups')), "*", "backup"),

                    # cache目录
                    (str(path_manager.get_path('cache')), "*", "cache"),

                    # temp目录
                    (str(path_manager.get_path('temp')), "*", "temp"),

                    # 基础目录中的文件
                    (base_dir, "*.txt", "data"),
                    (base_dir, "*.db", "data"),
                    (base_dir, "*.json", "config"),
                    (base_dir, "*.ini", "config"),
                ]

                # 现在所有备份都在统一的backups目录下，会被上面的扫描包含

                # 兼容性：也扫描项目目录中的文件（用于迁移检测）
                current_dir = os.path.dirname(os.path.abspath(__file__))
                legacy_patterns = [
                    (current_dir, "requirements.txt", "config"),
                    (current_dir, "package-lock.json", "config"),
                ]
                scan_patterns.extend(legacy_patterns)

            except ImportError:
                # 如果新系统不可用，使用旧的扫描方式
                current_dir = os.path.dirname(os.path.abspath(__file__))
                scan_patterns = [
                    (current_dir, "cursor_accounts.txt", "data"),
                    (current_dir, "cursor_accounts_backup.txt", "backup"),
                    (current_dir, "cursor_pro.db", "data"),
                    (current_dir, "block_domain.txt", "data"),
                    (current_dir, "config.ini", "config"),
                    (current_dir, "settings.json", "config"),
                    (current_dir, "register_config.json", "config"),
                    (current_dir, "requirements.txt", "config"),
                    (current_dir, "package-lock.json", "config"),
                ]

            # 只添加最重要的Cursor文件（不要扫描整个系统）
            try:
                paths_result = self.detect_cursor_paths()
                if paths_result["success"]:
                    cursor_paths = paths_result["data"]

                    # 只添加最核心的Cursor文件
                    cursor_scan_patterns = [
                        # 只要最重要的存储文件
                        (cursor_paths["globalStorageDir"], "storage.json", "config"),
                        (cursor_paths["globalStorageDir"], "state.vscdb", "data"),

                        # 只要machineId（这个确实重要）
                        (cursor_paths["cursorDataDir"], "machineId", "data"),
                    ]

                    # Windows特殊路径处理（只检查machineId）
                    if os.name == 'nt':
                        appdata = os.getenv('APPDATA', '')
                        if appdata:
                            windows_patterns = [
                                (os.path.join(appdata, 'Cursor'), "machineId", "data"),
                            ]
                            cursor_scan_patterns.extend(windows_patterns)

                    scan_patterns.extend(cursor_scan_patterns)
            except Exception as e:
                # Cursor路径检测失败不影响项目文件扫描
                pass

            # 不要扫描整个备份目录，太多文件了！
            # 用户只需要看到核心文件，不需要看到所有备份

            debug_info = []
            total_size = 0  # 添加总大小计算

            for base_path, pattern, file_type in scan_patterns:
                try:
                    debug_info.append(f"扫描: {base_path} / {pattern}")

                    # 检查路径是否存在和可访问
                    if not base_path or not os.path.exists(base_path):
                        debug_info.append(f"  路径不存在: {base_path}")
                        continue

                    # 检查路径访问权限
                    try:
                        os.listdir(base_path)
                    except PermissionError:
                        debug_info.append(f"  权限不足: {base_path}")
                        continue
                    except Exception as perm_error:
                        debug_info.append(f"  访问失败: {base_path} - {perm_error}")
                        continue

                    search_path = os.path.join(base_path, pattern)

                    # 使用更安全的glob搜索
                    try:
                        found_files = glob.glob(search_path)
                    except Exception as glob_error:
                        debug_info.append(f"  搜索失败: {search_path} - {glob_error}")
                        continue

                    debug_info.append(f"  找到 {len(found_files)} 个匹配文件")

                    for file_path in found_files:
                        try:
                            if not os.path.isfile(file_path):
                                continue

                            # 安全地获取文件信息
                            stat = os.stat(file_path)
                            size = stat.st_size
                            mtime = datetime.fromtimestamp(stat.st_mtime)

                            file_info = {
                                "name": os.path.basename(file_path),
                                "path": file_path,
                                "size": self._format_file_size(size),
                                "size_bytes": size,
                                "modified": mtime.strftime('%Y-%m-%d %H:%M:%S'),
                                "type": file_type,
                                "status": "normal",
                                "description": self._get_file_description(os.path.basename(file_path))
                            }

                            files.append(file_info)
                            total_size += size
                            debug_info.append(f"    ✅ {file_info['name']} ({file_info['size']})")

                        except PermissionError:
                            debug_info.append(f"    ⚠️ 权限不足: {os.path.basename(file_path)}")
                            continue
                        except Exception as file_error:
                            debug_info.append(f"    ❌ 读取失败: {os.path.basename(file_path)} - {file_error}")
                            continue

                except Exception as e:
                    debug_info.append(f"  ❌ 扫描异常: {e}")
                    continue

            # 按文件类型分类统计
            file_types = {}
            for file_info in files:
                file_type = file_info['type']
                if file_type not in file_types:
                    file_types[file_type] = {'count': 0, 'size': 0}
                file_types[file_type]['count'] += 1
                file_types[file_type]['size'] += file_info.get('size_bytes', 0)

            return {
                "success": True,
                "data": {
                    "files": files,
                    "count": len(files),
                    "total_size": total_size,
                    "total_size_formatted": self._format_file_size(total_size),
                    "file_types": file_types,
                    "debug": debug_info,
                    "current_dir": current_dir,
                    "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

        except Exception as e:
            return {"success": False, "error": f"扫描配置文件失败: {e}"}

    def get_storage_usage(self):
        """获取存储使用情况 - 基于实际扫描的文件"""
        try:
            # 直接使用扫描配置文件的结果
            scan_result = self.scan_config_files()

            if scan_result.get("success") and scan_result.get("data"):
                files_data = scan_result["data"]
                total_size = files_data.get("total_size", 0)
                files_list = files_data.get("files", [])

                # 按文件类型分组计算大小
                breakdown = {}
                for file_info in files_list:
                    file_type = file_info.get("type", "其他")
                    file_size = file_info.get("size_bytes", 0)

                    if file_type not in breakdown:
                        breakdown[file_type] = 0
                    breakdown[file_type] += file_size

                # 获取磁盘信息
                try:
                    import shutil
                    import os
                    disk_usage = shutil.disk_usage(os.getcwd())
                    disk_total = disk_usage.total
                    disk_free = disk_usage.free
                except:
                    disk_total = max(total_size * 1000, 1073741824)  # 至少1GB
                    disk_free = disk_total - total_size

                usage_percent = (total_size / disk_total * 100) if disk_total > 0 else 0

                return {
                    "success": True,
                    "totalSize": disk_total,
                    "usedSize": total_size,
                    "freeSize": disk_free,
                    "usagePercent": round(usage_percent, 4),
                    "breakdown": breakdown,
                    "file_count": len(files_list)
                }

            # 如果扫描失败，返回最小默认值
            return {
                "success": True,
                "totalSize": 1073741824,  # 1GB
                "usedSize": 0,
                "freeSize": 1073741824,
                "usagePercent": 0,
                "breakdown": {},
                "file_count": 0
            }

        except Exception as e:
            # 异常情况返回最小默认值
            return {
                "success": True,
                "totalSize": 1073741824,  # 1GB
                "usedSize": 0,
                "freeSize": 1073741824,
                "usagePercent": 0,
                "breakdown": {},
                "file_count": 0
            }

    def _format_file_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 B"

        units = ['B', 'KB', 'MB', 'GB', 'TB']
        i = 0
        while bytes_size >= 1024 and i < len(units) - 1:
            bytes_size /= 1024
            i += 1

        return f"{bytes_size:.1f} {units[i]}"

    def _get_directory_size(self, path):
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        continue
        except:
            pass
        return total_size

    def _get_file_description(self, filename):
        """获取文件描述"""
        descriptions = {
            # Cursor核心文件
            "storage.json": "Cursor全局存储配置",
            "state.vscdb": "Cursor状态数据库",
            "state.vscdb.new": "Cursor状态数据库(新版本)",
            "machineId": "Cursor机器标识文件",
            "machine-id": "Cursor机器标识文件",

            # 项目文件
            "cursor_accounts.txt": "账户信息文件",
            "register_config.json": "注册配置文件",
            "config.ini": "主配置文件",
            "settings.json": "用户设置文件",
            "cursor_pro.db": "项目数据库文件",

            # 备份文件
            "cursor_accounts_backup": "账户备份文件",

            # 系统文件
            "keybindings.json": "快捷键配置",
            "snippets": "代码片段",
            "extensions.json": "扩展配置",
            "package.json": "包配置文件",
            "package-lock.json": "包锁定文件",
            "requirements.txt": "Python依赖文件",
            "block_domain.txt": "域名阻止列表",

            # 日志文件（只保留重要的）
            "main.log": "主程序日志",
            "renderer.log": "渲染器日志",
            "app.log": "应用程序日志",
            "error.log": "错误日志",
            "auth.log": "认证日志",
            "api.log": "API调用日志"
        }

        # 精确匹配
        if filename in descriptions:
            return descriptions[filename]

        # 部分匹配
        for key, desc in descriptions.items():
            if key in filename.lower():
                return desc

        # 按扩展名和关键词分类
        if filename.endswith('.log'):
            return "日志文件"
        elif filename.endswith('.json'):
            return "JSON配置文件"
        elif filename.endswith('.vscdb'):
            return "VSCode数据库文件"
        elif filename.endswith('.db'):
            return "数据库文件"
        elif filename.endswith('.txt'):
            return "文本数据文件"
        elif filename.endswith('.ini'):
            return "配置文件"
        elif filename.endswith('.tmp'):
            return "临时文件"
        elif filename.endswith('.lock'):
            return "锁定文件"
        elif 'cache' in filename.lower():
            return "缓存文件"
        elif 'backup' in filename.lower():
            return "备份文件"
        elif 'temp' in filename.lower():
            return "临时文件"
        else:
            return "数据文件"



class SimpleOptimizedHandler(BaseHTTPRequestHandler):
    """简化优化版处理器"""
    
    # 单例API实例
    _api_instance = None
    
    @classmethod
    def get_api_instance(cls):
        """获取API实例（单例模式）"""
        if cls._api_instance is None:
            cls._api_instance = SimpleOptimizedAPI()
        return cls._api_instance
    
    def log_message(self, format, *args):
        """简化日志"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """处理GET请求 - 只处理API请求"""
        if self.path.startswith('/api/'):
            self._handle_api_request()
        else:
            # 前后端分离：不再服务静态文件
            self._send_error_response(404, "API Only - No Static Files")
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            self._handle_api_request()
        else:
            self._send_error_response(404, "Not Found")
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS）"""
        self.send_response(200)
        self._send_cors_headers()
        self.end_headers()
    
    def _handle_api_request(self):
        """处理API请求"""
        try:
            import urllib.parse
            api = self.get_api_instance()
            api_path = self.path[5:]  # 去掉 '/api/' 前缀

            # URL解码，处理下划线等特殊字符
            api_path = urllib.parse.unquote(api_path)

            # 处理数据API路由 /api/data/{key}
            if api_path.startswith('data/'):
                self._handle_data_api_request(api_path[5:])  # 去掉 'data/' 前缀
                return

            # 特殊路由处理 - 兼容前端路径
            if api_path == 'account/info':
                api_method = 'get_account_info'
            else:
                api_method = api_path

            # 读取POST数据
            post_data = None
            if self.command == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_body = self.rfile.read(content_length)
                    try:
                        post_data = json.loads(post_body.decode('utf-8'))
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析POST数据: {post_body}")

            # 特殊处理delete_account方法
            if api_method == 'delete_account':
                if post_data and 'email' in post_data:
                    result = api._delete_account_impl(email=post_data['email'])
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "delete_account 需要提供 email 参数"}
            # 特殊处理restore_account方法
            elif api_method == 'restore_account':
                if post_data and 'backup_file' in post_data:
                    result = api._restore_account_impl(backup_file=post_data['backup_file'])
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "restore_account 需要提供 backup_file 参数"}
            # 特殊处理delete_backup方法
            elif api_method == 'delete_backup':
                if post_data and 'backup_name' in post_data:
                    result = api.delete_backup(backup_name=post_data['backup_name'])
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "delete_backup 需要提供 backup_name 参数"}
            # 特殊处理list_backups方法
            elif api_method == 'list_backups':
                result = api._list_backups_impl()
                response_data = {"success": True, "data": result}
            # 特殊处理delete_account_backup方法
            elif api_method == 'delete_account_backup':
                if post_data and 'backup_file' in post_data:
                    result = api._delete_account_backup_impl(backup_file=post_data['backup_file'])
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "delete_account_backup 需要提供 backup_file 参数"}
            # 特殊处理save_register_config方法
            elif api_method == 'save_register_config':
                if post_data:
                    result = api.save_register_config(post_data)
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "save_register_config 需要提供配置数据"}
            # 特殊处理get_register_config方法
            elif api_method == 'get_register_config':
                result = api.get_register_config()
                response_data = {"success": True, "data": result}
            # 特殊处理restore_account_from_backup方法
            elif api_method == 'restore_account_from_backup':
                if post_data and 'email' in post_data:
                    result = api.restore_account_from_backup(email=post_data['email'])
                    response_data = {"success": True, "data": result}
                else:
                    response_data = {"success": False, "error": "restore_account_from_backup 需要提供 email 参数"}
            # 特殊处理list_backup_accounts方法
            elif api_method == 'list_backup_accounts':
                result = api.list_backup_accounts()
                response_data = {"success": True, "data": result}
            # 检查方法是否存在
            elif hasattr(api, api_method):
                method = getattr(api, api_method)

                # 根据是否有POST数据调用方法
                if post_data is not None:
                    result = method(post_data)
                else:
                    result = method()

                response_data = {"success": True, "data": result}
            else:
                response_data = {"success": False, "error": f"API方法 {api_method} 不存在"}

            self._send_json_response(response_data)

        except Exception as e:
            # 对于Cursor未登录错误，静默处理，不记录重复错误
            error_msg = str(e)
            if "未找到Cursor登录token" not in error_msg and "请先登录Cursor" not in error_msg:
                logger.error(f"API请求处理失败: {e}")
            self._send_json_response({"success": False, "error": str(e)})

    def _handle_data_api_request(self, data_key):
        """处理数据API请求 /api/data/{key}"""
        try:
            api = self.get_api_instance()

            # 读取POST数据（如果有）
            post_data = None
            if self.command == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_body = self.rfile.read(content_length)
                    try:
                        post_data = json.loads(post_body.decode('utf-8'))
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析POST数据: {post_body}")

            if self.command == 'GET':
                # GET请求：获取数据
                result = self._get_data_by_key(data_key)
                if result is not None:
                    self._send_json_response({"success": True, "data": result})
                else:
                    self._send_json_response({"success": False, "error": f"数据 {data_key} 不存在"})

            elif self.command == 'POST':
                # POST请求：设置数据
                if post_data is not None:
                    success = self._set_data_by_key(data_key, post_data)
                    if success:
                        self._send_json_response({"success": True, "message": f"数据 {data_key} 保存成功"})
                    else:
                        self._send_json_response({"success": False, "error": f"保存数据 {data_key} 失败"})
                else:
                    self._send_json_response({"success": False, "error": "POST请求需要提供数据"})

            else:
                self._send_json_response({"success": False, "error": f"不支持的HTTP方法: {self.command}"})

        except Exception as e:
            logger.error(f"数据API请求处理失败: {e}")
            self._send_json_response({"success": False, "error": str(e)})

    def _get_data_by_key(self, key):
        """根据键获取数据"""
        try:
            api = self.get_api_instance()

            # 映射数据键到对应的API方法
            key_mapping = {
                'cursor-accounts': 'get_registered_accounts',
                'app-config': 'show_config',
                'register-config': 'get_register_config',
                'cursor-account-info': 'get_account_info'
            }

            if key in key_mapping:
                method_name = key_mapping[key]
                if hasattr(api, method_name):
                    method = getattr(api, method_name)
                    result = method()
                    # 如果结果是字典且包含success字段，提取data部分
                    if isinstance(result, dict) and 'success' in result:
                        return result.get('data') if result.get('success') else None
                    return result
                else:
                    logger.warning(f"API方法 {method_name} 不存在")
                    return None
            else:
                logger.warning(f"未知的数据键: {key}")
                return None

        except Exception as e:
            logger.error(f"获取数据失败 [{key}]: {e}")
            return None

    def _set_data_by_key(self, key, data):
        """根据键设置数据"""
        try:
            api = self.get_api_instance()

            # 映射数据键到对应的API方法
            key_mapping = {
                'cursor-accounts': '_save_cursor_accounts',
                'app-config': '_save_app_config',
                'register-config': 'save_register_config',
                'cursor-account-info': '_save_cursor_account_info'
            }

            if key in key_mapping:
                method_name = key_mapping[key]
                if hasattr(api, method_name):
                    method = getattr(api, method_name)
                    result = method(data)
                    # 如果结果是字典且包含success字段，返回success状态
                    if isinstance(result, dict) and 'success' in result:
                        return result.get('success', False)
                    return True
                else:
                    logger.warning(f"API方法 {method_name} 不存在")
                    return False
            else:
                logger.warning(f"未知的数据键: {key}")
                return False

        except Exception as e:
            logger.error(f"设置数据失败 [{key}]: {e}")
            return False

    # 移除静态文件服务 - 前后端分离架构
    # 不再需要 _serve_static_file 和 _get_content_type 方法
    
    def _send_json_response(self, data):
        """发送JSON响应（带连接错误处理）"""
        try:
            response = json.dumps(data, ensure_ascii=False)

            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self._send_cors_headers()
            self.end_headers()
            self.wfile.write(response.encode('utf-8'))
        except ConnectionResetError:
            # 连接被重置（可能是前端界面被关闭）
            logger.warning("⚠️ 客户端连接已断开，可能是界面被意外关闭")
        except Exception as e:
            logger.error(f"❌ 发送响应失败: {e}")
            # 静默处理，避免进一步的错误
    
    def _send_error_response(self, code, message):
        """发送错误响应"""
        error_data = {"success": False, "error": message}
        response = json.dumps(error_data, ensure_ascii=False)
        
        self.send_response(code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self._send_cors_headers()
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def _send_cors_headers(self):
        """发送CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

def start_simple_optimized_server():
    """启动纯API服务器 - 前后端分离架构"""
    try:
        PORT = 8080
        server = HTTPServer(("", PORT), SimpleOptimizedHandler)

        logger.info(f"🌐 纯API服务器启动在 http://localhost:{PORT}")
        logger.info("⚡ 优化功能：模块缓存 + 结果缓存 + 单例模式")
        logger.info("🔗 前后端分离：前端请访问 http://localhost:3000")

        # 启动服务器
        logger.info("🔄 按 Ctrl+C 停止服务器")
        server.serve_forever()

    except KeyboardInterrupt:
        logger.info("\n🛑 正在停止服务器...")
        server.shutdown()
        return True
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        return False

def main():
    """主函数"""
    print("Cursor Pro - API Backend Server")
    print("=" * 40)
    print("Frontend-Backend Separation:")
    print("  - Backend: http://localhost:8080 (Pure API)")
    print("  - Frontend: http://localhost:3000 (Vue Dev Server)")
    print("Optimizations Enabled:")
    print("  - Module caching (99% performance boost)")
    print("  - Result caching (reduce duplicate calculations)")
    print("  - Singleton pattern (avoid duplicate creation)")
    print("Removed Features:")
    print("  - Static file serving (frontend-backend separation)")
    print("  - Complex database operations")
    print("  - Thread pool (unnecessary for single user)")
    print("=" * 40)

    return start_simple_optimized_server()

if __name__ == '__main__':
    main()
