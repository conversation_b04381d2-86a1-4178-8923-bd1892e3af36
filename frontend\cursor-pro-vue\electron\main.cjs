const { app, BrowserWindow, Menu, ipcMain } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

// Windows 圆角支持
let ffi, ref
try {
  if (process.platform === 'win32') {
    // 尝试加载 Windows API 支持
    console.log('Loading Windows API support for rounded corners')
  }
} catch (error) {
  console.log('Windows API support not available:', error.message)
}
console.log('isDev:', isDev, 'NODE_ENV:', process.env.NODE_ENV, 'isPackaged:', app.isPackaged)

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口将自动关闭
let mainWindow

function createWindow() {
  // 先设置应用菜单为null
  Menu.setApplicationMenu(null)

  // 创建浏览器窗口 - 强制无边框配置
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    // 跨平台一致的无边框设计
    frame: false, // 所有平台都使用无边框
    titleBarStyle: process.platform === 'darwin' ? 'customButtonsOnHover' : 'hidden',
    titleBarOverlay: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.cjs'),
      devTools: isDev, // 开发模式启用开发者工具
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    icon: path.join(__dirname, '../public/favicon.ico'),
    show: false, // 先不显示，等加载完成后再显示
    backgroundColor: '#1a1a1a',
    resizable: true,
    autoHideMenuBar: true, // 隐藏菜单栏
    skipTaskbar: false,
    transparent: false,
    hasShadow: true,
    thickFrame: true, // 改为 true 以支持 Windows 11 圆角
    enableLargerThanScreen: false,
    // 跨平台圆角窗口配置
    roundedCorners: true,
    // Windows特定配置
    ...(process.platform === 'win32' && {
      // Windows 11 背景材质效果
      backgroundMaterial: 'mica'
    }),
    // macOS特定配置
    ...(process.platform === 'darwin' && {
      // macOS 毛玻璃效果
      vibrancy: 'under-window',
      // 确保圆角生效
      roundedCorners: true
    }),
    // Linux特定配置
    ...(process.platform === 'linux' && {
      // Linux 基本配置（圆角支持有限，依赖桌面环境）
      roundedCorners: true
    })
  })

  // 立即强制设置无边框和隐藏菜单
  if (mainWindow.setWindowButtonVisibility) {
    mainWindow.setWindowButtonVisibility(false)
  }

  // 强制隐藏菜单栏 - Windows特殊处理
  mainWindow.setMenuBarVisibility(false)
  mainWindow.setAutoHideMenuBar(true)

  // 额外的强制隐藏方法
  if (process.platform === 'win32') {
    mainWindow.setMenu(null)
  }

  // 在内容加载前再次确保菜单隐藏
  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready - hiding menu bar')
    mainWindow.setMenuBarVisibility(false)
    mainWindow.setAutoHideMenuBar(true)
    if (process.platform === 'win32') {
      mainWindow.setMenu(null)
    }
    Menu.setApplicationMenu(null)
  })

  // 加载应用
  if (isDev) {
    // 开发模式：加载开发服务器 - 动态检测端口
    const devServerUrl = process.env.VITE_DEV_SERVER_URL || 'http://localhost:3000'
    console.log('Loading dev server from:', devServerUrl)
    mainWindow.loadURL(devServerUrl)
    // 开发模式自动打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产模式：加载构建后的文件
    const indexPath = path.join(__dirname, '../dist/index.html')
    console.log('Loading index.html from:', indexPath)
    mainWindow.loadFile(indexPath)
    // 生产模式不打开开发者工具
    // mainWindow.webContents.openDevTools()
  }

  // 窗口加载完成后显示
  mainWindow.once('ready-to-show', () => {
    // 强制移除边框和菜单栏（Windows 特定）
    if (process.platform === 'win32') {
      mainWindow.setMenuBarVisibility(false)
      mainWindow.setAutoHideMenuBar(true)
    }

    // 再次确保菜单栏隐藏
    mainWindow.setMenuBarVisibility(false)
    mainWindow.setAutoHideMenuBar(true)

    // Windows特殊处理
    if (process.platform === 'win32') {
      mainWindow.setMenu(null)
    }

    mainWindow.show()

    // Windows 11 圆角设置
    if (process.platform === 'win32') {
      try {
        // 尝试设置 Windows 11 圆角
        const os = require('os')
        const osVersion = os.release()
        console.log('Windows version:', osVersion)

        // Windows 11 (build 22000+) 支持圆角
        if (parseInt(osVersion.split('.')[2]) >= 22000) {
          console.log('Windows 11 detected, attempting to enable rounded corners')

          // 使用 Windows API 设置圆角
          setTimeout(() => {
            try {
              const { exec } = require('child_process')
              const hwnd = mainWindow.getNativeWindowHandle()
              if (hwnd) {
                console.log('Setting rounded corners for window')
                // 这里可以调用 Windows API 来设置圆角
                // 目前依赖于系统的默认圆角行为
              }
            } catch (apiError) {
              console.log('Could not set rounded corners via API:', apiError.message)
            }
          }, 1000)
        }
      } catch (error) {
        console.log('Could not detect Windows version:', error.message)
      }
    }

    // 显示后再次确保隐藏
    setTimeout(() => {
      mainWindow.setMenuBarVisibility(false)
      mainWindow.setAutoHideMenuBar(true)
      if (process.platform === 'win32') {
        mainWindow.setMenu(null)
      }
    }, 100)

    // 如果是开发模式，聚焦到窗口
    if (isDev) {
      mainWindow.focus()
    }
  })

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口，通常会把多个window对象存放在一个数组里，与此同时，你应该删除相应的元素
    mainWindow = null
  })

  // 处理窗口控制
  ipcMain.handle('window-minimize', () => {
    mainWindow.minimize()
  })

  ipcMain.handle('window-maximize', async () => {
    const wasMaximized = mainWindow.isMaximized()
    console.log('🔼 主进程收到最大化请求，当前状态:', wasMaximized ? '最大化' : '正常')

    // 防止重复操作
    if (mainWindow._isToggling) {
      console.log('⚠️ 正在切换中，忽略重复请求')
      return { success: false, maximized: wasMaximized }
    }

    mainWindow._isToggling = true

    try {
      if (wasMaximized) {
        console.log('📤 执行还原操作')
        mainWindow.unmaximize()
      } else {
        console.log('📥 执行最大化操作')
        mainWindow.maximize()
      }

      // 等待操作完成
      await new Promise(resolve => setTimeout(resolve, 200))

      // 验证操作结果
      const newState = mainWindow.isMaximized()
      console.log('✅ 操作完成，新状态:', newState ? '最大化' : '正常')

      return { success: true, maximized: newState }
    } finally {
      // 清除锁定状态
      setTimeout(() => {
        mainWindow._isToggling = false
      }, 300)
    }
  })

  ipcMain.handle('window-close', () => {
    mainWindow.close()
  })

  ipcMain.handle('window-is-maximized', () => {
    return mainWindow.isMaximized()
  })

  // 防抖函数
  let stateChangeTimeout = null

  // 监听窗口状态变化事件
  mainWindow.on('maximize', () => {
    console.log('🔥 窗口已最大化事件触发')
    clearTimeout(stateChangeTimeout)
    stateChangeTimeout = setTimeout(() => {
      mainWindow.webContents.send('window-state-changed', { maximized: true })
    }, 50)
  })

  mainWindow.on('unmaximize', () => {
    console.log('🔥 窗口已还原事件触发')
    clearTimeout(stateChangeTimeout)
    stateChangeTimeout = setTimeout(() => {
      mainWindow.webContents.send('window-state-changed', { maximized: false })
    }, 50)
  })
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
// 某些API只能在此事件发生后使用
app.whenReady().then(() => {
  // 立即隐藏菜单栏
  Menu.setApplicationMenu(null)

  createWindow()

  // 再次确保菜单栏隐藏
  Menu.setApplicationMenu(null)

  app.on('activate', () => {
    // 在macOS上，当单击dock图标并且没有其他窗口打开时，通常会在应用程序中重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用程序及其菜单栏通常保持活动状态，直到用户明确退出Cmd + Q
  if (process.platform !== 'darwin') app.quit()
})

// 在此文件中，您可以包含应用程序的其余特定主进程代码。您也可以将它们放在单独的文件中并在此处require它们
