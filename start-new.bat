@echo off
chcp 65001 >nul 2>&1
title Cursor Pro - Complete Launcher
color 0A

echo ========================================
echo   Cursor Pro - Starting Application
echo ========================================
echo.

echo [1/3] Setting Python path...
set PYTHONPATH=%cd%\src
echo [OK] PYTHONPATH set

echo.
echo [2/3] Starting backend API server...
start "Cursor Pro API" cmd /c "set PYTHONPATH=%cd%\src && python -m cursor_pro.core.api_server"

echo [OK] Backend API server starting...
echo Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak >nul

echo.
echo [3/3] Starting frontend Electron application...
cd frontend\cursor-pro-vue
npm run electron:dev

echo.
echo Application started!
pause
