from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import random
from colorama import Fore, Style
import cursor_pro.management.config as configparser
from pathlib import Path
import sys
from cursor_pro.management.config import get_config
from cursor_pro.utils.common import get_default_browser_path as utils_get_default_browser_path

# Add global variable at the beginning of the file
_translator = None

# Add global variable to track our Chrome processes
_chrome_process_ids = []

# 防检测系统将在运行时动态加载
ANTI_DETECTION_ENABLED = False

# 导入日志系统
try:
    import cursor_pro.anti_detection.machine_reset as reset_machine_manual
    def log_to_gui(msg):
        """将消息同时输出到控制台和GUI日志"""
        print(msg)
        # 移除颜色码后发送到GUI日志
        import re
        clean_msg = re.sub(r'\x1b\[[0-9;]*m', '', str(msg))
        reset_machine_manual.log(f"[注册] {clean_msg}")
except ImportError:
    def log_to_gui(msg):
        """如果无法导入日志系统，只输出到控制台"""
        print(msg)

def cleanup_chrome_processes(translator=None):
    """Clean only Chrome processes launched by this script"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        log_to_gui("\nNo Chrome processes to clean...")
        return

    log_to_gui("\nCleaning Chrome processes launched by this script...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    import signal
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []  # Reset the list after cleanup
    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.cleanup_error', error=str(e))}{Style.RESET_ALL}")
        else:
            log_to_gui(f"清理进程时出错: {e}")

def signal_handler(signum, frame):
    """Handle Ctrl+C signal"""
    global _translator
    if _translator:
        log_to_gui(f"{Fore.CYAN}{_translator.get('register.exit_signal')}{Style.RESET_ALL}")
    else:
        log_to_gui("\n接收到退出信号，正在关闭...")
    cleanup_chrome_processes(_translator)
    os._exit(0)

def wait_for_oauth_completion(page, max_wait=15):
    """智能等待OAuth2认证完成"""
    log_to_gui(f"{Fore.CYAN}🔄 等待OAuth2认证流程完成...{Style.RESET_ALL}")

    start_time = time.time()
    while time.time() - start_time < max_wait:
        current_url = page.url

        # 检查是否已经完成OAuth2回调
        success_indicators = [
            "cursor.com/dashboard",
            "cursor.com/settings",
            "cursor.com/agents",
            "api/auth/callback"
        ]

        for indicator in success_indicators:
            if indicator in current_url:
                log_to_gui(f"{Fore.GREEN}✅ OAuth2认证完成: {current_url}{Style.RESET_ALL}")
                return True

        # 如果在Cursor主站但不是注册页面，也认为成功
        if "cursor.com" in current_url and "sign-up" not in current_url and "authenticator" not in current_url:
            log_to_gui(f"{Fore.GREEN}✅ 已跳转到Cursor主站: {current_url}{Style.RESET_ALL}")
            return True

        time.sleep(1)

    log_to_gui(f"{Fore.YELLOW}⏰ OAuth2等待超时，当前页面: {page.url}{Style.RESET_ALL}")
    return False

def simulate_human_input(page, url, config, translator=None):
    """Visit URL"""
    if translator:
        log_to_gui(f"{Fore.CYAN}🚀 {translator.get('register.visiting_url')}: {url}{Style.RESET_ALL}")

    # First visit blank page
    page.get('about:blank')
    time.sleep(get_random_wait_time(config, 'page_load_wait'))

    # Visit target page
    page.get(url)
    time.sleep(get_random_wait_time(config, 'page_load_wait'))

def fill_signup_form(page, first_name, last_name, email, config, translator=None):
    """Fill signup form with anti-detection support"""
    try:
        # Try to use anti-detection if available
        try:
            from cursor_pro.anti_detection.integration import smart_fill_signup_form, should_continue_registration, ANTI_DETECTION_AVAILABLE
            if ANTI_DETECTION_AVAILABLE and should_continue_registration():
                log_to_gui(f"{Fore.GREEN}🛡️ 使用防检测模式填写表单{Style.RESET_ALL}")
                return smart_fill_signup_form(page, first_name, last_name, email, config, translator)
        except ImportError:
            pass
        except Exception as e:
            log_to_gui(f"{Fore.YELLOW}⚠️ 防检测功能异常，使用原始模式: {e}{Style.RESET_ALL}")

        # Use original method
        if translator:
            log_to_gui(f"{Fore.CYAN}📧 {translator.get('register.filling_form')}{Style.RESET_ALL}")
        else:
            log_to_gui("\n正在填写注册表单（原始模式）...")

        # Fill first name
        first_name_input = page.ele("@name=first_name")
        if first_name_input:
            first_name_input.input(first_name)
            time.sleep(get_random_wait_time(config, 'input_wait'))

        # Fill last name
        last_name_input = page.ele("@name=last_name")
        if last_name_input:
            last_name_input.input(last_name)
            time.sleep(get_random_wait_time(config, 'input_wait'))

        # Fill email
        email_input = page.ele("@name=email")
        if email_input:
            email_input.input(email)
            time.sleep(get_random_wait_time(config, 'input_wait'))

        # Click submit button
        submit_button = page.ele("@type=submit")
        if submit_button:
            submit_button.click()
            time.sleep(get_random_wait_time(config, 'submit_wait'))

        if translator:
            log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.form_success')}{Style.RESET_ALL}")
        else:
            log_to_gui("Form filled successfully")
        return True

    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.form_error', error=str(e))}{Style.RESET_ALL}")
        else:
            log_to_gui(f"Error filling form: {e}")
        return False

def get_user_documents_path():
    """Get user Documents folder path"""
    if sys.platform == "win32":
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                documents_path, _ = winreg.QueryValueEx(key, "Personal")
                return documents_path
        except Exception as e:
            # fallback
            return os.path.join(os.path.expanduser("~"), "Documents")
    elif sys.platform == "darwin":
        return os.path.join(os.path.expanduser("~"), "Documents")
    else:  # Linux
        # Get actual user's home directory
        sudo_user = os.environ.get('SUDO_USER')
        if sudo_user:
            return os.path.join("/home", sudo_user, "Documents")
        return os.path.join(os.path.expanduser("~"), "Documents")

def get_random_wait_time(config, timing_type='page_load_wait'):
    """
    Get random wait time from config
    Args:
        config: ConfigParser object
        timing_type: Type of timing to get (page_load_wait, input_wait, submit_wait)
    Returns:
        float: Random wait time or fixed time
    """
    try:
        if not config.has_section('Timing'):
            return random.uniform(0.1, 0.8)  # Default value
            
        if timing_type == 'random':
            min_time = float(config.get('Timing', 'min_random_time', fallback='0.1'))
            max_time = float(config.get('Timing', 'max_random_time', fallback='0.8'))
            return random.uniform(min_time, max_time)
            
        time_value = config.get('Timing', timing_type, fallback='0.1-0.8')
        
        # Check if it's a fixed time value
        if '-' not in time_value and ',' not in time_value:
            return float(time_value)  # Return fixed time
            
        # Process range time
        min_time, max_time = map(float, time_value.split('-' if '-' in time_value else ','))
        return random.uniform(min_time, max_time)
    except:
        return random.uniform(0.1, 0.8)  # Return default value when error

def setup_driver(translator=None):
    """Setup browser driver"""
    global _chrome_process_ids
    
    try:
        # Get config
        config = get_config(translator)
        
        # Get browser type and path
        browser_type = config.get('Browser', 'default_browser', fallback='chrome')
        browser_path = config.get('Browser', f'{browser_type}_path', fallback=utils_get_default_browser_path(browser_type))
        
        if not browser_path or not os.path.exists(browser_path):
            if translator:
                log_to_gui(f"{Fore.YELLOW}⚠️ {browser_type} {translator.get('register.browser_path_invalid')}{Style.RESET_ALL}")
            browser_path = utils_get_default_browser_path(browser_type)

        # For backward compatibility, also check Chrome path
        if browser_type == 'chrome':
            chrome_path = config.get('Chrome', 'chromepath', fallback=None)
            if chrome_path and os.path.exists(chrome_path):
                browser_path = chrome_path

        # Set browser options
        co = ChromiumOptions()

        # Set browser path
        co.set_browser_path(browser_path)

        # Use incognito mode
        co.set_argument("--incognito")

        if sys.platform == "linux":
            # Set Linux specific options
            co.set_argument("--no-sandbox")

        # Set random port
        co.auto_port()

        # Use headless mode (must be set to False, simulate human operation)
        co.headless(False)

        # Log browser info
        if translator:
            log_to_gui(f"{Fore.CYAN}🌐 {translator.get('register.using_browser', browser=browser_type, path=browser_path)}{Style.RESET_ALL}")

        try:
            # Load extension
            extension_path = os.path.join(os.getcwd(), "turnstilePatch")
            if os.path.exists(extension_path):
                co.set_argument("--allow-extensions-in-incognito")
                co.add_extension(extension_path)
        except Exception as e:
            if translator:
                log_to_gui(f"{Fore.RED}❌ {translator.get('register.extension_load_error', error=str(e))}{Style.RESET_ALL}")
            else:
                log_to_gui(f"Error loading extension: {e}")

        if translator:
            log_to_gui(f"{Fore.CYAN}🚀 {translator.get('register.starting_browser')}{Style.RESET_ALL}")
        else:
            log_to_gui("Starting browser...")
        
        # Record Chrome processes before launching
        before_pids = []
        try:
            import psutil
            browser_process_names = {
                'chrome': ['chrome', 'chromium'],
                'edge': ['msedge', 'edge'],
                'firefox': ['firefox'],
                'brave': ['brave', 'brave-browser']
            }
            process_names = browser_process_names.get(browser_type, ['chrome'])
            before_pids = [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in process_names)]
        except:
            pass
            
        # Launch browser
        page = ChromiumPage(co)

        # Wait a moment for browser to fully launch
        time.sleep(1)
        
        # Record browser processes after launching and find new ones
        try:
            import psutil
            process_names = browser_process_names.get(browser_type, ['chrome'])
            after_pids = [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in process_names)]
            # Find new browser processes
            new_pids = [pid for pid in after_pids if pid not in before_pids]
            _chrome_process_ids.extend(new_pids)
            
            if _chrome_process_ids:
                if translator:
                    log_to_gui(f"{translator.get('register.tracking_processes', count=len(_chrome_process_ids), browser=browser_type)}")
                else:
                    log_to_gui(f"Tracking {len(_chrome_process_ids)} {browser_type} processes")
            else:
                if translator:
                    log_to_gui(f"{Fore.YELLOW}Warning: {translator.get('register.no_new_processes_detected', browser=browser_type)}{Style.RESET_ALL}")
                else:
                    log_to_gui(f"{Fore.YELLOW}Warning: No new {browser_type} processes detected{Style.RESET_ALL}")
        except Exception as e:
            if translator:
                log_to_gui(f"{translator.get('register.could_not_track_processes', browser=browser_type, error=str(e))}")
            else:
                log_to_gui(f"Could not track {browser_type} processes: {e}")

        return config, page

    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.browser_setup_error', error=str(e))}{Style.RESET_ALL}")
        else:
            log_to_gui(f"Error setting up browser: {e}")
        raise



def handle_turnstile(page, config, translator=None):
    """Handle Turnstile verification"""
    try:
        if translator:
            log_to_gui(f"{Fore.CYAN}🔄 {translator.get('register.handling_turnstile')}{Style.RESET_ALL}")
        else:
            log_to_gui("\nHandling Turnstile verification...")

        # from config
        turnstile_time = float(config.get('Turnstile', 'handle_turnstile_time', fallback='2'))
        random_time_str = config.get('Turnstile', 'handle_turnstile_random_time', fallback='1-3')

        # Parse random time range
        try:
            min_time, max_time = map(float, random_time_str.split('-'))
        except:
            min_time, max_time = 1, 3  # Default value

        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            retry_count += 1
            if translator:
                log_to_gui(f"{Fore.CYAN}🔄 {translator.get('register.retry_verification', attempt=retry_count)}{Style.RESET_ALL}")
            else:
                log_to_gui(f"Attempt {retry_count} of verification...")

            try:
                # Try to reset turnstile
                page.run_js("try { turnstile.reset() } catch(e) { }")
                time.sleep(turnstile_time)  # from config

                # Locate verification box element
                challenge_check = (
                    page.ele("@id=cf-turnstile", timeout=2)
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )

                if challenge_check:
                    if translator:
                        log_to_gui(f"{Fore.CYAN}🔄 {translator.get('register.detect_turnstile')}{Style.RESET_ALL}")
                    else:
                        log_to_gui("Detected verification box...")

                    # from config
                    time.sleep(random.uniform(min_time, max_time))
                    challenge_check.click()
                    time.sleep(turnstile_time)  # from config

                    # check verification result
                    if check_verification_success(page, translator):
                        if translator:
                            log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.verification_success')}{Style.RESET_ALL}")
                        else:
                            log_to_gui("Verification successful!")
                        return True

            except Exception as e:
                if translator:
                    log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_failed')}{Style.RESET_ALL}")
                else:
                    log_to_gui(f"Verification attempt failed: {e}")

            # Check if verification has been successful
            if check_verification_success(page, translator):
                if translator:
                    log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.verification_success')}{Style.RESET_ALL}")
                else:
                    log_to_gui("Verification successful!")
                return True

            time.sleep(random.uniform(min_time, max_time))

        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_failed')}{Style.RESET_ALL}")
        else:
            log_to_gui("Exceeded maximum retry attempts")
        return False

    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_error', error=str(e))}{Style.RESET_ALL}")
        else:
            log_to_gui(f"Error in verification process: {e}")
        return False

    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_error', error=str(e))}{Style.RESET_ALL}")
        else:
            log_to_gui(f"Error in verification process: {e}")
        return False

def check_verification_success(page, translator=None):
    """Check if verification is successful"""
    try:
        # Check current URL for success indicators
        current_url = page.url
        if current_url:
            # 简单直接的成功判断：只有真正跳转到cursor.com域名才算成功
            if current_url.startswith("https://cursor.com/") and "authenticator" not in current_url:
                if translator:
                    log_to_gui(f"{Fore.GREEN}✅ 注册成功！已跳转到: {current_url}{Style.RESET_ALL}")
                return True

            # 还在认证服务器就是失败
            if "authenticator.cursor.sh" in current_url:
                if translator:
                    log_to_gui(f"{Fore.RED}❌ 注册失败，还在认证页面: {current_url}{Style.RESET_ALL}")
                return False

        # Check if there is a subsequent form element, indicating verification has passed
        if (page.ele("@name=password", timeout=0.5) or
            page.ele("@name=email", timeout=0.5) or
            page.ele("@data-index=0", timeout=0.5) or
            page.ele("Account Settings", timeout=0.5) or
            page.ele("Agents", timeout=0.5) or
            page.ele("Dashboard", timeout=0.5)):
            return True

        # Check if there is an error message
        error_messages = [
            'xpath://div[contains(text(), "Can\'t verify the user is human")]',
            'xpath://div[contains(text(), "Error: 600010")]',
            'xpath://div[contains(text(), "Please try again")]'
        ]

        for error_xpath in error_messages:
            if page.ele(error_xpath):
                return False

        return False
    except:
        return False



def generate_password(length=12):
    """Generate random password"""
    chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    return ''.join(random.choices(chars, k=length))

def fill_password(page, password: str, config, translator=None):
    """
    Fill password form
    """
    try:
        log_to_gui(f"{Fore.CYAN}🔑 {translator.get('register.setting_password') if translator else 'Setting password'}{Style.RESET_ALL}")

        # Fill password
        password_input = page.ele("@name=password")
        log_to_gui(f"{Fore.CYAN}🔑 {translator.get('register.setting_on_password')}: {password}{Style.RESET_ALL}")
        if password_input:
            password_input.input(password)

        # Click submit button
        submit_button = page.ele("@type=submit")
        if submit_button:
            submit_button.click()
            time.sleep(get_random_wait_time(config, 'submit_wait'))

        log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.password_submitted') if translator else 'Password submitted'}{Style.RESET_ALL}")

        return True

    except Exception as e:
        log_to_gui(f"{Fore.RED}❌ {translator.get('register.password_error', error=str(e)) if translator else f'Error setting password: {str(e)}'}{Style.RESET_ALL}")

        return False

def handle_verification_code(browser_tab, email_tab, controller, config, translator=None):
    """Handle verification code"""
    try:
        if translator:
            log_to_gui(f"\n{Fore.CYAN}🔄 {translator.get('register.waiting_for_verification_code')}{Style.RESET_ALL}")

        # Check if using manual input verification code
        if hasattr(controller, 'get_verification_code') and email_tab is None:  # Manual mode
            verification_code = controller.get_verification_code()
            if verification_code:
                # Fill verification code in registration page
                for i, digit in enumerate(verification_code):
                    browser_tab.ele(f"@data-index={i}").input(digit)
                    time.sleep(get_random_wait_time(config, 'verification_code_input'))

                log_to_gui(f"{translator.get('register.verification_success')}")
                time.sleep(get_random_wait_time(config, 'verification_success_wait'))

                # Handle last Turnstile verification
                if handle_turnstile(browser_tab, config, translator):
                    if translator:
                        log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.verification_success')}{Style.RESET_ALL}")
                    time.sleep(get_random_wait_time(config, 'verification_retry_wait'))

                    # 简单直接的最终检查
                    current_url = browser_tab.url
                    log_to_gui(f"{Fore.CYAN}🔍 最终检查页面: {current_url}{Style.RESET_ALL}")

                    # 只有真正跳转到cursor.com才算成功
                    if current_url.startswith("https://cursor.com/") and "authenticator" not in current_url:
                        log_to_gui(f"{Fore.GREEN}✅ 注册成功！{Style.RESET_ALL}")
                        log_to_gui(f"{Fore.GREEN}🎉 账户已创建，可以正常使用{Style.RESET_ALL}")
                        return True, browser_tab
                    else:
                        log_to_gui(f"{Fore.RED}❌ 注册失败！未能完成OAuth2认证{Style.RESET_ALL}")
                        log_to_gui(f"{Fore.YELLOW}💡 账户可能已创建，但需要手动登录验证{Style.RESET_ALL}")
                        return False, browser_tab

                    return True, browser_tab

                return False, None

        # Automatic verification code logic
        elif email_tab:
            log_to_gui(f"{Fore.CYAN}🔄 {translator.get('register.waiting_for_verification_code')}{Style.RESET_ALL}")
            time.sleep(get_random_wait_time(config, 'email_check_initial_wait'))

            # Use existing email_tab to refresh email
            email_tab.refresh_inbox()
            time.sleep(get_random_wait_time(config, 'email_refresh_wait'))

            # Check if there is a verification code email
            if email_tab.check_for_cursor_email():
                verification_code = email_tab.get_verification_code()
                if verification_code:
                    # Fill verification code in registration page
                    for i, digit in enumerate(verification_code):
                        browser_tab.ele(f"@data-index={i}").input(digit)
                        time.sleep(get_random_wait_time(config, 'verification_code_input'))

                    if translator:
                        log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.verification_success')}{Style.RESET_ALL}")
                    time.sleep(get_random_wait_time(config, 'verification_success_wait'))

                    # Handle last Turnstile verification
                    if handle_turnstile(browser_tab, config, translator):
                        if translator:
                            log_to_gui(f"{Fore.GREEN}✅ {translator.get('register.verification_success')}{Style.RESET_ALL}")
                        time.sleep(get_random_wait_time(config, 'verification_retry_wait'))

                        # Check current URL for registration completion
                        current_url = browser_tab.url
                        if translator:
                            log_to_gui(f"{Fore.CYAN}🔍 当前页面: {current_url}{Style.RESET_ALL}")

                        # Registration completed successfully, no need to force redirect
                        if "cursor.com" in current_url:
                            log_to_gui(f"{Fore.GREEN}✅ 注册流程已完成{Style.RESET_ALL}")
                        else:
                            log_to_gui(f"{Fore.BLUE}ℹ️ 注册完成，当前页面: {current_url}{Style.RESET_ALL}")

                        return True, browser_tab

                    else:
                        if translator:
                            log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_failed')}{Style.RESET_ALL}")
                        else:
                            log_to_gui("最后一次验证失败")
                        return False, None

            return False, None

    except Exception as e:
        if translator:
            log_to_gui(f"{Fore.RED}❌ {translator.get('register.verification_error', error=str(e))}{Style.RESET_ALL}")
        return False, None

def main(email=None, password=None, first_name=None, last_name=None, email_tab=None, controller=None, translator=None):
    """Main function, can receive account information, email tab, and translator"""
    global _translator
    global _chrome_process_ids
    _translator = translator  # Save to global variable
    _chrome_process_ids = []  # Reset the process IDs list

    # Only set signal handlers if running in main thread (not in GUI)
    try:
        import threading
        import signal
        if threading.current_thread() is threading.main_thread():
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
    except Exception:
        # If signal setup fails, continue without it (GUI mode)
        pass
    
    page = None
    success = False
    try:
        config, page = setup_driver(translator)
        if translator:
            log_to_gui(f"{Fore.CYAN}🚀 {translator.get('register.browser_started')}{Style.RESET_ALL}")

        # Visit registration page
        url = "https://authenticator.cursor.sh/sign-up"

        # Visit page
        simulate_human_input(page, url, config, translator)
        if translator:
            log_to_gui(f"{Fore.CYAN}🔄 {translator.get('register.waiting_for_page_load')}{Style.RESET_ALL}")
        time.sleep(get_random_wait_time(config, 'page_load_wait'))
        
        # If account information is not provided, generate random information
        if not all([email, password, first_name, last_name]):
            first_name = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=6)).capitalize()
            last_name = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=6)).capitalize()
            email = f"{first_name.lower()}{random.randint(100,999)}@example.com"
            password = generate_password()
            
            # Save account information
            with open('test_accounts.txt', 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*50}\n")
                f.write(f"Email: {email}\n")
                f.write(f"Password: {password}\n")
                f.write(f"{'='*50}\n")
        
        # Fill form
        if fill_signup_form(page, first_name, last_name, email, config, translator):
            if translator:
                log_to_gui(f"\n{Fore.GREEN}✅ {translator.get('register.form_submitted')}{Style.RESET_ALL}")

            # Handle first Turnstile verification
            if handle_turnstile(page, config, translator):
                if translator:
                    log_to_gui(f"\n{Fore.GREEN}✅ {translator.get('register.first_verification_passed')}{Style.RESET_ALL}")

                # Fill password
                if fill_password(page, password, config, translator):
                    if translator:
                        log_to_gui(f"\n{Fore.CYAN}🔄 {translator.get('register.waiting_for_second_verification')}{Style.RESET_ALL}")

                    # Handle second Turnstile verification
                    if handle_turnstile(page, config, translator):
                        if translator:
                            log_to_gui(f"\n{Fore.CYAN}🔄 {translator.get('register.waiting_for_verification_code')}{Style.RESET_ALL}")
                        success_result, final_page = handle_verification_code(page, email_tab, controller, config, translator)
                        if success_result:
                            success = True
                            return True, final_page
                        else:
                            log_to_gui(f"\n{Fore.RED}❌ {translator.get('register.verification_code_processing_failed') if translator else 'Verification code processing failed'}{Style.RESET_ALL}")
                    else:
                        log_to_gui(f"\n{Fore.RED}❌ {translator.get('register.second_verification_failed') if translator else 'Second verification failed'}{Style.RESET_ALL}")
                else:
                    log_to_gui(f"\n{Fore.RED}❌ {translator.get('register.password_error') if translator else 'Password setting failed'}{Style.RESET_ALL}")
            else:
                log_to_gui(f"\n{Fore.RED}❌ {translator.get('register.first_verification_failed') if translator else 'First verification failed'}{Style.RESET_ALL}")

        return False, None

    except Exception as e:
        log_to_gui(f"发生错误: {e}")
        return False, None
    finally:
        if page and not success:  # Only clean up when failed
            try:
                page.quit()
            except:
                pass
            cleanup_chrome_processes(translator)

if __name__ == "__main__":
    main()  # Run without parameters, use randomly generated information 