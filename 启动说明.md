# 🚀 Cursor Pro 启动指南

## 📋 可用的启动脚本

### 1. `start-electron.bat` - 标准启动 (推荐)
**用途**: 日常使用的标准启动方式
**特点**:
- ✅ 智能启动 - 检测是否已构建，避免重复构建
- ✅ 快速启动 - 第二次启动无需重新构建
- ✅ 基本环境检查
- ✅ 自动启动后端和前端
- ✅ 用户友好的界面

**使用方法**: 双击运行即可
**启动逻辑**:
- 首次启动: 自动构建Vue项目 → 启动Electron
- 后续启动: 直接启动Electron (跳过构建步骤)

---

### 2. `debug-start.bat` - 诊断模式
**用途**: 当遇到问题时使用，进行详细的环境检查
**特点**:
- 🔍 详细的环境诊断
- 🔍 逐步检查所有依赖
- 🔍 自动安装缺失的依赖
- 🔍 详细的错误提示和解决建议
- ✅ 同样支持智能启动优化

**使用方法**: 当标准启动失败时使用

---

## 🛠️ 环境要求

### 必需软件
- **Python 3.7+**: [下载地址](https://python.org)
- **Node.js 16+**: [下载地址](https://nodejs.org)
- **Git**: [下载地址](https://git-scm.com)

### 可选软件
- **Visual Studio Code**: 用于代码编辑
- **Chrome/Edge**: 用于OAuth认证

---

## 🚨 常见问题

### 问题1: "未找到Python"
**解决方案**: 
1. 安装Python 3.7或更高版本
2. 确保Python已添加到系统PATH
3. 重启命令行/重启电脑

### 问题2: "未找到npm"
**解决方案**:
1. 安装Node.js (会自动包含npm)
2. 确保Node.js已添加到系统PATH
3. 重启命令行

### 问题3: "npm install失败"
**解决方案**:
1. 检查网络连接
2. 尝试使用国内镜像: `npm config set registry https://registry.npmmirror.com`
3. 清理缓存: `npm cache clean --force`

---

## 📞 获取帮助

如果遇到其他问题:
1. 首先使用 `debug-start.bat` 进行诊断
2. 查看详细的错误信息
3. 根据提示进行相应的修复

---

## ⚡ 启动优化说明

### 🚀 智能构建机制
- **首次启动**: 会自动检测并构建Vue项目 (约1-2分钟)
- **后续启动**: 直接使用已构建的文件 (约5-10秒)
- **强制重建**: 删除 `cursor-pro-vue/dist` 文件夹即可

### 📊 启动时间对比
- **优化前**: 每次启动都需要1-2分钟构建
- **优化后**: 首次1-2分钟，后续5-10秒

---

## 🎯 快速开始

1. **首次使用**: 运行 `debug-start.bat` 确保环境正确
2. **日常使用**: 运行 `start-electron.bat` 快速启动
3. **遇到问题**: 再次运行 `debug-start.bat` 进行诊断
4. **强制重建**: 删除 `cursor-pro-vue/dist` 后重新启动
