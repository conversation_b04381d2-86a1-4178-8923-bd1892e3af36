#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro - Python 后端打包脚本
使用 PyInstaller 将 Python 后端打包成独立可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查 PyInstaller 是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def create_spec_file():
    """创建 PyInstaller 配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有 Python 文件
a = Analysis(
    ['vue_with_api_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[
        # 包含配置文件和数据文件
        ('*.json', '.'),
        ('*.txt', '.'),
        ('*.md', '.'),
        ('locales', 'locales'),
        ('database', 'database'),
        ('api', 'api'),
        ('email_tabs', 'email_tabs'),
        ('images', 'images'),
        ('public', 'public'),
        ('docs', 'docs'),
    ],
    hiddenimports=[
        # 显式导入所有模块
        'version_control',
        'legacy_path_adapter',
        'data_path_manager',
        'account_manager',
        'advanced_anti_detection',
        'anti_detection_integration',
        'backup_session',
        'backup_settings',
        'bypass_token_limit',
        'bypass_version',
        'config',
        'cursor_acc_info',
        'cursor_auth',
        'cursor_pro_anti_detection',
        'cursor_register_manual',
        'enhanced_new_signup',
        'get_user_token',
        'main',
        'new_signup',
        'oauth_auth',
        'parameter_tuning',
        'reset_machine_manual',
        'utils',
        'flask',
        'flask_cors',
        'requests',
        'selenium',
        'playwright',
        'undetected_chromedriver',
        'colorama',
        'pandas',
        'openpyxl',
        'psutil',
        'configparser',
        'python_dotenv',
        'dateutil',
        'ujson',
        'cryptography',
        'urllib3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='cursor-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='images/logo.png' if os.path.exists('images/logo.png') else None,
)
'''
    
    with open('cursor-backend.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建 PyInstaller 配置文件: cursor-backend.spec")

def build_backend():
    """构建后端可执行文件"""
    print("🚀 开始构建 Python 后端...")
    
    # 检查 PyInstaller
    if not check_pyinstaller():
        return False
    
    # 创建配置文件
    create_spec_file()
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的构建文件")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 执行构建
    try:
        print("📦 正在打包...")
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "cursor-backend.spec"
        ])
        
        if os.path.exists('dist/cursor-backend.exe'):
            print("✅ 后端打包成功!")
            print(f"📁 可执行文件位置: {os.path.abspath('dist/cursor-backend.exe')}")
            
            # 复制必要的文件到 dist 目录
            copy_essential_files()
            return True
        else:
            print("❌ 打包失败，未找到可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包过程中出错: {e}")
        return False

def copy_essential_files():
    """复制必要文件到 dist 目录"""
    essential_files = [
        'requirements.txt',
        'README.md',
        'DISCLAIMER.md',
        'SECURITY.md',
    ]
    
    essential_dirs = [
        'locales',
        'database',
        'api',
        'images',
        'docs',
    ]
    
    dist_dir = Path('dist')
    
    # 复制文件
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir / file)
            print(f"📄 复制文件: {file}")
    
    # 复制目录
    for dir_name in essential_dirs:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, dist_dir / dir_name, dirs_exist_ok=True)
            print(f"📁 复制目录: {dir_name}")

def main():
    """主函数"""
    print("=" * 50)
    print("🏗️  Cursor Pro - Python 后端打包工具")
    print("=" * 50)
    
    if build_backend():
        print("\n🎉 后端打包完成!")
        print("📦 打包文件位于 'dist' 目录")
        print("🚀 可以直接运行 'dist/cursor-backend.exe' 启动后端服务")
    else:
        print("\n❌ 后端打包失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
