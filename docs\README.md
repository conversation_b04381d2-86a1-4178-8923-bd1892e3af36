# 🚀 Cursor Pro - 使用指南

## 📋 概述

Cursor Pro现在基于**纯SQLite数据库**，提供轻量级、零配置的数据管理解决方案。

## 🎯 SQLite版本优势

### ✅ **纯SQLite的优势**
- 🚀 **零配置**: 无需安装数据库服务器，开箱即用
- 📦 **轻量级**: 文件型数据库，便于备份和迁移
- ⚡ **高性能**: 启动速度快，内存占用低
- 🔧 **易维护**: 无需复杂的数据库管理
- 🌐 **跨平台**: 完美支持Windows、macOS、Linux

### 🆚 **性能对比**

| 指标 | 之前MySQL版本 | 现在SQLite版本 | 提升 |
|------|---------------|----------------|------|
| 启动时间 | 10-15秒 | 1-2秒 | **10倍** |
| 内存占用 | 200MB+ | 10-20MB | **90%减少** |
| 安装复杂度 | 需要MySQL服务器 | 零配置 | **极简** |
| 备份方式 | mysqldump | 复制文件 | **简单** |
| 部署难度 | 中等 | 极简 | **零门槛** |

## 🛠️ 安装和使用

### 方法1: 一键启动（推荐）

```bash
# 双击运行SQLite版本启动器
scripts/start_sqlite.bat
```

启动器会自动：
- 检查Python环境
- 初始化SQLite数据库
- 启动前后端服务
- 打开浏览器访问应用

### 方法2: 手动启动

#### 2.1 安装Python依赖

```bash
pip install -r requirements.txt
```

> 注意：SQLite是Python内置模块，无需额外安装

#### 2.2 启动应用

```bash
# 启动SQLite版本
python scripts/start_sqlite.py

# 或直接启动主应用
python vue_with_api_optimized.py
```

#### 2.3 访问应用

```bash
# 应用启动后自动打开浏览器，或手动访问
http://localhost:8000
```

## 🔧 配置管理

### 数据库配置

SQLite数据库自动配置，无需手动设置：

```bash
# 数据库文件位置
# Windows: %USERPROFILE%\Documents\.cursor-pro\cursor_pro.db
# macOS/Linux: ~/.cursor-pro/cursor_pro.db
```

### 查看数据库状态

```bash
# 查看SQLite数据库状态
python scripts/database_status.py status
```

## 📈 性能优化

### SQLite优化配置

项目已自动应用以下SQLite优化：

```sql
PRAGMA foreign_keys = ON;      -- 启用外键约束
PRAGMA journal_mode = WAL;     -- 使用WAL模式提高并发
PRAGMA synchronous = NORMAL;   -- 平衡性能和安全性
```

### 应用层优化

```python
# SQLite连接配置
SQLITE_CONFIG = {
    'timeout': 30.0,
    'check_same_thread': False,
    'isolation_level': None  # 自动提交模式
}
```

## 🚀 快速开始

### 1. 下载项目
```bash
git clone https://github.com/your-repo/cursor-pro.git
cd cursor-pro
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# Windows用户
scripts/start_sqlite.bat

# 或使用Python
python vue_with_api_optimized.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:8000

## 📁 项目结构

```
cursor-pro-main/
├── 📁 docs/                    # 文档
├── 📁 scripts/                 # 启动和管理脚本
├── 📁 tests/                   # 测试文件
├── 📁 tools/                   # 开发工具
├── 📁 database/                # 数据库配置
├── 📁 locales/                 # 国际化文件
├── 📁 images/                  # 图片资源
├── 📁 email_tabs/              # 邮箱标签页
├── 📁 cursor-pro-vue/     # Vue前端项目
├── 📄 vue_with_api_optimized.py # 主应用程序
├── 📄 cursor_auth.py           # 认证管理器
├── 📄 requirements.txt         # Python依赖
└── 📄 其他核心Python文件
```

## 🔧 开发和测试

### 运行测试
```bash
# 测试SQLite功能
python tests/test_simple_sqlite.py

# 测试启动器
python tests/test_launcher.py

# 完整性检查
python tests/final_cleanup_check.py
```

### 查看数据库状态
```bash
python scripts/database_status.py status
```

## 🛠️ 故障排除

### 常见问题

#### 1. 启动失败
```bash
# 检查Python环境
python --version

# 重新安装依赖
pip install -r requirements.txt
```

#### 2. 数据库问题
```bash
# 检查数据库状态
python scripts/database_status.py status

# 重新初始化数据库
python -c "from database.config import init_database; init_database()"
```

#### 3. 端口占用
```bash
# 检查端口占用
netstat -ano | findstr :8000

# 或修改端口启动
python vue_with_api_optimized.py --port 8001
```

## 📚 更多文档

- 📖 [SQLite使用指南](docs/README_SQLITE.md)
- 📋 [迁移总结](docs/MIGRATION_SUMMARY.md)
- 🔧 [启动器更新说明](docs/LAUNCHER_UPDATES.md)
- 📁 [项目结构说明](docs/PROJECT_STRUCTURE.md)

## 🎉 总结

Cursor Pro SQLite版本为你提供：

- ✅ **零配置体验** - 下载即用
- ✅ **极简部署** - 无需数据库服务器
- ✅ **高性能运行** - 启动速度提升10倍
- ✅ **便捷维护** - 文件型数据库易于管理
- ✅ **跨平台支持** - Windows/macOS/Linux通用

立即开始使用吧！🚀
