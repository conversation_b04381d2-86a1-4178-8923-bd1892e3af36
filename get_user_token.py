import os
import requests
import json
import logging
import time
from colorama import Fore, Style
from config import get_config

# Define emoji constants
EMOJI = {
    'START': '🚀',
    'OAUTH': '🔑',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'WAIT': '⏳',
    'INFO': 'ℹ️',
    'WARNING': '⚠️'
}

def refresh_token(token, translator, max_retries=3, retry_delay=1):
    """
    刷新用户Token
    :param token: 当前Token
    :param translator: 翻译器实例
    :param max_retries: 最大重试次数
    :param retry_delay: 重试延迟（秒）
    :return: 刷新后的Token或None
    """
    if not token:
        logging.error(translator.get("backup_session.refresh_token_no_token_error"))
        return None

    config = get_config()
    
    # 记录刷新尝试
    logging.info(translator.get("backup_session.refresh_token_attempt"))
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'User-Agent': 'Cursor/0.10.0',
        'Accept': 'application/json',
    }
    
    # 多个刷新端点，按顺序尝试
    refresh_endpoints = [
        "https://cursor.sh/api/auth/refresh",
        "https://api.cursor.sh/auth/refresh",
        "https://api.cursor.so/auth/refresh"
    ]
    
    for retry in range(max_retries):
        for endpoint in refresh_endpoints:
            try:
                # 添加SSL错误处理和更多的请求选项
                response = requests.post(
                    endpoint,
                    headers=headers,
                    json={},
                    timeout=15,
                    verify=True,  # 验证SSL证书
                    allow_redirects=True
                )

                if response.status_code == 200:
                    try:
                        result = response.json()
                        new_token = result.get('accessToken')
                        if new_token and len(new_token) > 50:
                            logging.info(translator.get("backup_session.refresh_token_success"))

                            # 验证新Token
                            if verify_token(new_token, translator):
                                return new_token
                            else:
                                logging.warning(translator.get("backup_session.refresh_token_verification_failed"))
                                continue
                        else:
                            logging.warning(translator.get("backup_session.refresh_token_invalid_response"))
                    except Exception as e:
                        logging.error(f"{translator.get('backup_session.refresh_token_parse_error')}: {str(e)}")
                elif response.status_code == 401:
                    logging.error(translator.get("backup_session.refresh_token_unauthorized"))
                    # Token无效，尝试下一个端点
                    continue
                else:
                    logging.warning(f"{translator.get('backup_session.refresh_token_failed')}: {response.status_code}")
            except requests.exceptions.SSLError as e:
                logging.error(f"SSL错误 - {endpoint}: {str(e)}")
                # SSL错误时跳过当前端点，尝试下一个
                continue
            except requests.exceptions.ConnectionError as e:
                logging.error(f"连接错误 - {endpoint}: {str(e)}")
                continue
            except requests.exceptions.Timeout as e:
                logging.error(f"超时错误 - {endpoint}: {str(e)}")
                continue
            except Exception as e:
                logging.error(f"{translator.get('backup_session.refresh_token_request_error')}: {str(e)}")
        
        # 如果所有端点都失败，但还有重试次数，则等待后重试
        if retry < max_retries - 1:
            logging.info(f"{translator.get('backup_session.refresh_token_retry')} {retry + 1}/{max_retries}")
            time.sleep(retry_delay)
    
    # 所有尝试都失败，检查原Token是否仍然有效
    if verify_token(token, translator):
        logging.info(translator.get("backup_session.refresh_token_original_valid"))
        return token
    
    logging.error(translator.get("backup_session.refresh_token_all_attempts_failed"))
    return None

def verify_token(token, translator):
    """验证Token是否有效"""
    if not token or len(token) < 50:
        return False
        
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': 'Cursor/0.10.0',
        }
        
        # 尝试访问需要认证的API端点
        verify_endpoints = [
            "https://cursor.sh/api/user/me",
            "https://api.cursor.sh/user/me",
            "https://cursor.com/api/user/me"
        ]

        for endpoint in verify_endpoints:
            try:
                response = requests.get(
                    endpoint,
                    headers=headers,
                    timeout=10,
                    verify=True
                )

                if response.status_code == 200:
                    return True
            except (requests.exceptions.SSLError,
                    requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout):
                continue

        return False
    except Exception as e:
        logging.error(f"{translator.get('backup_session.verify_token_error')}: {str(e)}")
        return False

def get_token_from_cookie(cookie_value, translator=None, skip_refresh=False):
    """Extract and process token from cookie value

    Args:
        cookie_value (str): The WorkosCursorSessionToken cookie value
        translator: Optional translator object
        skip_refresh (bool): Skip token refresh for new registrations

    Returns:
        str: The processed token
    """
    try:
        # For new registrations, skip refresh and use direct extraction
        if skip_refresh:
            if '%3A%3A' in cookie_value:
                return cookie_value.split('%3A%3A')[-1]
            elif '::' in cookie_value:
                return cookie_value.split('::')[-1]
            else:
                return cookie_value

        # Try to refresh the token with the API first (only for existing tokens)
        try:
            refreshed_token = refresh_token(cookie_value, translator)

            # If refresh succeeded and returned a different token, use it
            if refreshed_token and refreshed_token != cookie_value:
                return refreshed_token
        except Exception as refresh_error:
            # If refresh fails, continue with extraction
            if translator:
                print(f"{Fore.YELLOW}⚠️ Token刷新失败，使用原始Token{Style.RESET_ALL}")

        # Use traditional extraction method
        if '%3A%3A' in cookie_value:
            return cookie_value.split('%3A%3A')[-1]
        elif '::' in cookie_value:
            return cookie_value.split('::')[-1]
        else:
            return cookie_value

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('token.extraction_error', error=str(e)) if translator else f'Error extracting token: {str(e)}'}{Style.RESET_ALL}")
        # Fall back to original behavior
        if '%3A%3A' in cookie_value:
            return cookie_value.split('%3A%3A')[-1]
        elif '::' in cookie_value:
            return cookie_value.split('::')[-1]
        else:
            return cookie_value