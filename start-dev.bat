@echo off
chcp 65001 >nul 2>&1
title Cursor Pro Development Environment

echo.
echo ========================================
echo    Cursor Pro Development Launcher
echo ========================================
echo.

echo [1/3] Starting backend API server...
start "Cursor Pro API Server" cmd /c "title Cursor Pro API && set PYTHONPATH=%cd%\src && python -m cursor_pro.core.api_server && pause"

echo [2/3] Waiting for API server to start...
timeout /t 3 /nobreak >nul

echo [3/3] Starting frontend development environment...
cd frontend\cursor-pro-vue

echo Starting Vite dev server and Electron...
echo Dev server will run at http://localhost:3000
echo Electron app will open automatically
echo.
echo Tips:
echo - Code changes will auto-reload
echo - Press Ctrl+C to stop dev server
echo - Closing Electron window won't stop dev server
echo.

npm run electron:dev

echo.
echo Development environment closed
pause
