#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 防检测集成模块
Integration of Anti-Detection System for Cursor Pro
"""

import random
import time
import json
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta

# 导入现有模块
from advanced_anti_detection import AdvancedAntiDetection, BrowserProfile

class CursorProAntiDetection:
    """Cursor Pro 专用防检测系统"""
    
    def __init__(self, config_path: str = "config.py"):
        self.detector = AdvancedAntiDetection()
        self.config_path = config_path
        self.success_rate_threshold = 0.7
        self.max_daily_registrations = 999999  # 取消每日限制
        self.registration_history = []
        
    def enhanced_signup_with_detection(self, page, email: str, password: str, config: dict) -> bool:
        """增强的注册流程，集成防检测机制"""
        try:
            print("🛡️ 启动防检测注册流程")
            
            # 1. 检查风险等级
            risk_info = self.detector.check_detection_risk()
            print(f"当前风险等级: {risk_info['risk_level']}")
            
            if risk_info['risk_level'] == '高':
                print("⚠️ 风险等级过高，建议暂停操作")
                return False
            
            # 2. 应用浏览器隐身设置
            self._apply_stealth_settings(page)
            
            # 4. 模拟人类访问行为
            self._simulate_natural_browsing(page)
            
            # 5. 智能等待页面加载
            self._smart_wait_for_page_load(page)
            
            # 6. 人类化填写表单
            success = self._fill_form_humanized(page, email, password, config)
            
            if success:
                # 7. 处理验证码（如果存在）
                self._handle_captcha_intelligently(page, config)
                
                # 8. 提交表单
                submit_success = self._submit_form_safely(page)
                
                if submit_success:
                    # 记录成功
                    self._record_registration_attempt(email, True)
                    self.detector.add_request_record("signup", True)
                    print("✅ 注册成功")
                    return True
                else:
                    self._record_registration_attempt(email, False)
                    self.detector.add_request_record("signup", False)
                    print("❌ 提交失败")
                    return False
            else:
                self._record_registration_attempt(email, False)
                self.detector.add_request_record("signup", False)
                print("❌ 表单填写失败")
                return False
                
        except Exception as e:
            print(f"❌ 注册过程异常: {e}")
            self._record_registration_attempt(email, False)
            self.detector.add_request_record("signup", False)
            return False
    

    def _apply_stealth_settings(self, page):
        """应用隐身设置"""
        try:
            # 注入防检测脚本
            stealth_script = """
            // 移除自动化标识
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪装Chrome对象
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 移除Selenium痕迹
            const originalDescriptor = Object.getOwnPropertyDescriptor(Navigator.prototype, 'webdriver');
            if (originalDescriptor) {
                delete Navigator.prototype.webdriver;
            }
            
            // 伪装权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            console.log('🛡️ Stealth settings applied');
            """
            
            page.run_js(stealth_script)
            print("✅ 隐身设置已应用")
            
        except Exception as e:
            print(f"⚠️ 隐身设置应用失败: {e}")
    
    def _simulate_natural_browsing(self, page):
        """模拟自然浏览行为"""
        print("🎭 模拟自然浏览行为...")
        
        # 随机滚动
        scroll_actions = random.randint(2, 5)
        for _ in range(scroll_actions):
            scroll_amount = random.randint(-200, 300)
            try:
                page.scroll(0, scroll_amount)
                time.sleep(random.uniform(0.5, 1.2))
            except:
                pass
        
        # 随机停留
        pause_time = random.uniform(2.0, 5.0)
        print(f"⏱️ 自然停留 {pause_time:.1f} 秒")
        time.sleep(pause_time)
    
    def _smart_wait_for_page_load(self, page):
        """智能等待页面加载"""
        print("⏳ 智能等待页面加载...")
        
        # 等待页面基本加载
        time.sleep(random.uniform(1.0, 3.0))
        
        # 检查页面是否完全加载
        max_wait = 10
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                # 检查页面状态
                ready_state = page.run_js("return document.readyState")
                if ready_state == "complete":
                    break
            except:
                pass
            
            time.sleep(0.5)
            wait_time += 0.5
        
        # 额外等待，模拟人类观察时间
        observation_time = random.uniform(1.0, 2.5)
        time.sleep(observation_time)
    
    def _fill_form_humanized(self, page, email: str, password: str, config: dict) -> bool:
        """人类化填写表单"""
        print("📝 开始人类化填写表单...")
        
        try:
            # 查找并填写邮箱
            email_selectors = [
                "@name=email",
                "input[type='email']",
                "#email",
                ".email-input"
            ]
            
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = page.ele(selector)
                    if email_input:
                        break
                except:
                    continue
            
            if email_input:
                print("📧 填写邮箱...")
                # 模拟点击聚焦
                email_input.click()
                time.sleep(random.uniform(0.3, 0.8))
                
                # 人类化输入
                self._human_type_text(email_input, email)
                
                # 短暂停顿
                time.sleep(random.uniform(0.5, 1.0))
            else:
                print("❌ 未找到邮箱输入框")
                return False
            
            # 查找并填写密码
            password_selectors = [
                "@name=password",
                "input[type='password']",
                "#password",
                ".password-input"
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = page.ele(selector)
                    if password_input:
                        break
                except:
                    continue
            
            if password_input:
                print("🔐 填写密码...")
                password_input.click()
                time.sleep(random.uniform(0.3, 0.8))
                
                self._human_type_text(password_input, password)
                time.sleep(random.uniform(0.5, 1.0))
            else:
                print("❌ 未找到密码输入框")
                return False
            
            # 填写其他字段（如姓名）
            self._fill_additional_fields(page)
            
            return True
            
        except Exception as e:
            print(f"❌ 表单填写失败: {e}")
            return False
    
    def _human_type_text(self, element, text: str):
        """人类化输入文本"""
        # 清空输入框
        element.clear()
        time.sleep(random.uniform(0.1, 0.3))
        
        # 逐字符输入
        for i, char in enumerate(text):
            element.input(char)
            
            # 随机打字速度
            typing_speed = random.uniform(0.05, 0.15)
            time.sleep(typing_speed)
            
            # 偶尔暂停（模拟思考）
            if random.random() < 0.1:
                time.sleep(random.uniform(0.3, 1.0))
            
            # 偶尔打错字然后纠正
            if random.random() < 0.03 and i > 0:
                # 输入错误字符
                wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                element.input(wrong_char)
                time.sleep(random.uniform(0.1, 0.3))
                
                # 删除错误字符
                element.input('\b')
                time.sleep(random.uniform(0.1, 0.3))
    
    def _fill_additional_fields(self, page):
        """填写其他必填字段"""
        # 查找姓名字段
        name_selectors = [
            "@name=first_name",
            "@name=firstName",
            "@name=name",
            "#first_name",
            "#firstName"
        ]
        
        for selector in name_selectors:
            try:
                name_input = page.ele(selector)
                if name_input:
                    print("👤 填写姓名...")
                    name_input.click()
                    time.sleep(random.uniform(0.2, 0.5))
                    
                    # 随机生成姓名
                    first_names = ["Alex", "Sam", "Jordan", "Taylor", "Casey", "Morgan", "Riley", "Avery"]
                    random_name = random.choice(first_names)
                    
                    self._human_type_text(name_input, random_name)
                    break
            except:
                continue
    
    def _handle_captcha_intelligently(self, page, config: dict):
        """智能处理验证码"""
        print("🔍 检查验证码...")
        
        # 等待验证码加载
        time.sleep(random.uniform(2.0, 4.0))
        
        # 查找Turnstile验证码
        turnstile_selectors = [
            "iframe[src*='turnstile']",
            ".cf-turnstile",
            "#cf-turnstile"
        ]
        
        for selector in turnstile_selectors:
            try:
                turnstile_element = page.ele(selector)
                if turnstile_element:
                    print("🤖 发现Turnstile验证码")
                    self._handle_turnstile_captcha(page, turnstile_element)
                    return
            except:
                continue
        
        # 查找其他类型验证码
        captcha_selectors = [
            ".captcha",
            "#captcha",
            "iframe[src*='recaptcha']"
        ]
        
        for selector in captcha_selectors:
            try:
                captcha_element = page.ele(selector)
                if captcha_element:
                    print("🔐 发现其他验证码")
                    # 模拟人类观察验证码的时间
                    time.sleep(random.uniform(3.0, 8.0))
                    return
            except:
                continue
        
        print("✅ 未发现验证码")
    
    def _handle_turnstile_captcha(self, page, turnstile_element):
        """处理Turnstile验证码"""
        try:
            # 模拟人类观察验证码
            observation_time = random.uniform(2.0, 5.0)
            print(f"👀 观察验证码 {observation_time:.1f} 秒")
            time.sleep(observation_time)
            
            # 尝试点击验证框
            turnstile_element.click()
            
            # 等待验证完成
            verification_wait = random.uniform(3.0, 8.0)
            print(f"⏳ 等待验证完成 {verification_wait:.1f} 秒")
            time.sleep(verification_wait)
            
        except Exception as e:
            print(f"⚠️ Turnstile处理异常: {e}")
    
    def _submit_form_safely(self, page) -> bool:
        """安全提交表单"""
        print("📤 准备提交表单...")
        
        # 最后检查
        final_check_time = random.uniform(1.0, 3.0)
        print(f"🔍 最后检查 {final_check_time:.1f} 秒")
        time.sleep(final_check_time)
        
        # 查找提交按钮
        submit_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "text:注册",
            "text:Sign Up",
            "text:Register",
            ".submit-btn",
            "#submit"
        ]
        
        submit_button = None
        for selector in submit_selectors:
            try:
                submit_button = page.ele(selector)
                if submit_button:
                    break
            except:
                continue
        
        if submit_button:
            # 模拟点击前的犹豫
            time.sleep(random.uniform(0.5, 1.5))
            
            print("🖱️ 点击提交按钮")
            submit_button.click()
            
            # 等待提交处理
            submit_wait = random.uniform(3.0, 8.0)
            print(f"⏳ 等待提交处理 {submit_wait:.1f} 秒")
            time.sleep(submit_wait)
            
            return True
        else:
            print("❌ 未找到提交按钮")
            return False
    
    def _record_registration_attempt(self, email: str, success: bool):
        """记录注册尝试"""
        self.registration_history.append({
            'email': email,
            'success': success,
            'date': datetime.now(),
            'session_id': self.detector.session_id
        })
        
        # 保持最近50条记录
        if len(self.registration_history) > 50:
            self.registration_history = self.registration_history[-50:]
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_attempts = len(self.registration_history)
        successful_attempts = sum(1 for reg in self.registration_history if reg['success'])
        
        success_rate = (successful_attempts / total_attempts) if total_attempts > 0 else 0
        
        # 今日统计
        today = datetime.now().date()
        today_attempts = [reg for reg in self.registration_history if reg['date'].date() == today]
        today_success = sum(1 for reg in today_attempts if reg['success'])
        
        risk_info = self.detector.check_detection_risk()
        
        return {
            'total_attempts': total_attempts,
            'successful_attempts': successful_attempts,
            'success_rate': f"{success_rate:.1%}",
            'today_attempts': len(today_attempts),
            'today_success': today_success,
            'daily_limit_remaining': '无限制',
            'risk_level': risk_info['risk_level'],
            'recommendation': risk_info['recommendation']
        }
    
    def should_continue_registration(self) -> bool:
        """判断是否应该继续注册"""
        stats = self.get_statistics()
        
        # 检查成功率
        if stats['total_attempts'] >= 5:
            success_rate = stats['successful_attempts'] / stats['total_attempts']
            if success_rate < self.success_rate_threshold:
                return False

        # 检查风险等级
        risk_info = self.detector.check_detection_risk()
        if risk_info['risk_level'] == '高':
            return False
        
        return True
    
    def get_safe_delay(self) -> float:
        """获取安全延迟时间"""
        base_delay = random.uniform(300, 900)  # 5-15分钟基础延迟
        
        # 根据风险等级调整
        risk_info = self.detector.check_detection_risk()
        if risk_info['risk_level'] == '高':
            return base_delay * 3
        elif risk_info['risk_level'] == '中':
            return base_delay * 2
        else:
            return base_delay

# 使用示例
def demo_cursor_pro_anti_detection():
    """演示Cursor Pro防检测系统"""
    print("🎯 Cursor Pro 防检测系统演示")
    print("=" * 50)
    
    anti_detection = CursorProAntiDetection()
    
    # 显示统计信息
    stats = anti_detection.get_statistics()
    print("📊 当前统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 检查是否可以继续
    can_continue = anti_detection.should_continue_registration()
    print(f"\n🚦 可以继续注册: {'是' if can_continue else '否'}")
    
    if can_continue:
        safe_delay = anti_detection.get_safe_delay()
        print(f"⏱️ 建议延迟: {safe_delay/60:.1f} 分钟")
    
    print("\n✅ Cursor Pro 防检测系统准备就绪！")

if __name__ == "__main__":
    demo_cursor_pro_anti_detection()
