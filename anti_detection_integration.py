#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 防检测集成入口
Anti-Detection Integration Entry Point
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 首先导入原始模块（总是需要）
from new_signup import fill_signup_form, handle_turnstile

# 导入防检测核心模块
try:
    from cursor_pro_anti_detection import CursorProAntiDetection
    from advanced_anti_detection import AdvancedAntiDetection

    print("🛡️ 防检测模块加载成功")
    ANTI_DETECTION_AVAILABLE = True

except ImportError as e:
    print(f"⚠️ 防检测模块加载失败: {e}")
    print("📝 将使用原始注册功能")
    ANTI_DETECTION_AVAILABLE = False

# 创建防检测实例
if ANTI_DETECTION_AVAILABLE:
    _anti_detection_instance = CursorProAntiDetection()

# 统一接口函数
def smart_fill_signup_form(page, first_name, last_name, email, config, translator=None):
    """智能表单填写 - 集成防检测功能"""
    if ANTI_DETECTION_AVAILABLE:
        print("🛡️ 使用防检测模式注册")

        # 应用防检测设置
        try:
            # 检查风险等级
            risk_info = _anti_detection_instance.detector.check_detection_risk()
            print(f"📊 当前风险等级: {risk_info['risk_level']}")

            if risk_info['risk_level'] == '高':
                print("⚠️ 风险等级过高，使用安全模式")

            # 应用浏览器隐身设置
            _apply_stealth_mode(page)

            # 使用原始填写功能，但添加防检测行为
            result = _fill_form_with_anti_detection(page, first_name, last_name, email, config, translator)

            # 记录操作结果
            _anti_detection_instance.detector.add_request_record("form_fill", result)

            return result

        except Exception as e:
            print(f"⚠️ 防检测功能异常: {e}")
            print("🔄 回退到原始模式")

    print("📝 使用原始注册功能")
    return _original_fill_form(page, first_name, last_name, email, config, translator)

def smart_handle_turnstile(page, config, translator=None):
    """智能Turnstile处理 - 集成防检测功能"""
    if ANTI_DETECTION_AVAILABLE:
        print("🛡️ 使用防检测模式处理验证码")
        try:
            # 添加人类观察时间
            import time, random
            observation_time = random.uniform(2.0, 4.0)
            print(f"👀 观察验证码 {observation_time:.1f} 秒")
            time.sleep(observation_time)

            return handle_turnstile(page, config, translator)
        except Exception as e:
            print(f"⚠️ 防检测验证码处理异常: {e}")

    print("🤖 使用原始Turnstile处理")
    return handle_turnstile(page, config, translator)

# 辅助函数
def _apply_stealth_mode(page):
    """应用隐身模式"""
    try:
        # 注入改进的防检测脚本
        stealth_script = """
        try {
            // 安全地处理webdriver属性
            if (navigator.webdriver !== undefined) {
                try {
                    delete navigator.webdriver;
                } catch(e) {
                    // 如果无法删除，尝试重新定义
                    try {
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            configurable: true
                        });
                    } catch(e2) {
                        // 忽略错误，继续其他操作
                    }
                }
            }

            // 伪装Chrome运行时（如果不存在）
            if (!window.chrome) {
                window.chrome = {
                    runtime: {},
                    app: { isInstalled: false },
                    webstore: {},
                };
            }

            // 伪装权限API
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(parameters) {
                    if (parameters.name === 'notifications') {
                        return Promise.resolve({ state: Notification.permission });
                    }
                    return originalQuery.apply(this, arguments);
                };
            }

            console.log('🛡️ 防检测模式已启用');
        } catch(e) {
            console.log('⚠️ 部分防检测功能启用失败:', e.message);
        }
        """

        if hasattr(page, 'run_js'):
            page.run_js(stealth_script)
        elif hasattr(page, 'execute_script'):
            page.execute_script(stealth_script)

        print("✅ 隐身模式已应用")
    except Exception as e:
        print(f"⚠️ 隐身模式应用失败，继续使用基础防检测: {str(e)[:100]}...")

def _fill_form_with_anti_detection(page, first_name, last_name, email, config, translator):
    """使用防检测技术填写表单"""
    import time, random

    try:
        print("🎭 应用人类行为模拟")

        # 添加随机延迟
        delay = random.uniform(1.0, 3.0)
        time.sleep(delay)

        # 直接实现表单填写，避免循环调用
        result = _original_fill_form(page, first_name, last_name, email, config, translator)

        # 添加完成后的自然停顿
        completion_delay = random.uniform(0.5, 1.5)
        time.sleep(completion_delay)

        print("✅ 防检测表单填写完成")
        return result

    except Exception as e:
        print(f"❌ 防检测表单填写失败: {e}")
        return False

def _original_fill_form(page, first_name, last_name, email, config, translator):
    """原始表单填写逻辑（避免循环调用）"""
    import time
    import random

    try:
        # 填写名字
        first_name_input = page.ele("@name=first_name")
        if first_name_input:
            first_name_input.input(first_name)
            time.sleep(random.uniform(0.5, 1.0))

        # 填写姓氏
        last_name_input = page.ele("@name=last_name")
        if last_name_input:
            last_name_input.input(last_name)
            time.sleep(random.uniform(0.5, 1.0))

        # 填写邮箱
        email_input = page.ele("@name=email")
        if email_input:
            email_input.input(email)
            time.sleep(random.uniform(0.5, 1.0))

        # 提交表单
        submit_button = page.ele("@type=submit") or page.ele("button:contains('Sign up')")
        if submit_button:
            submit_button.click()
            time.sleep(random.uniform(1.0, 2.0))

        return True

    except Exception as e:
        print(f"❌ 原始表单填写失败: {e}")
        return False

def get_registration_statistics():
    """获取注册统计信息"""
    if ANTI_DETECTION_AVAILABLE:
        return _anti_detection_instance.get_statistics()
    else:
        return {
            "message": "防检测模块未启用",
            "anti_detection_available": False
        }

def should_continue_registration():
    """判断是否应该继续注册"""
    if ANTI_DETECTION_AVAILABLE:
        return _anti_detection_instance.should_continue_registration()
    else:
        return True  # 原始版本没有限制

# 导出主要函数
__all__ = [
    'smart_fill_signup_form',
    'smart_handle_turnstile', 
    'get_registration_statistics',
    'should_continue_registration',
    'ANTI_DETECTION_AVAILABLE'
]

if __name__ == "__main__":
    print("🚀 Cursor Pro 防检测集成测试")
    print("=" * 50)
    
    print(f"防检测模块状态: {'✅ 可用' if ANTI_DETECTION_AVAILABLE else '❌ 不可用'}")
    
    if ANTI_DETECTION_AVAILABLE:
        stats = get_registration_statistics()
        print("📊 当前统计:")
        for key, value in stats.items():
            if key != 'current_risk':
                print(f"  {key}: {value}")
        
        can_continue = should_continue_registration()
        print(f"🚦 可以继续注册: {'是' if can_continue else '否'}")
    
    print("✅ 集成测试完成")
