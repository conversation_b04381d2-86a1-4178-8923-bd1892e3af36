# 🚀 Cursor Pro 项目重构迁移指南

## 📋 概述

本文档说明了Cursor Pro项目从原始结构到标准Python包结构的重构过程和变化。

## 🏗️ 项目结构变化

### 旧结构 (重构前)
```
cursor-pro/
├── main.py
├── vue_with_api_optimized.py
├── cursor_auth.py
├── backup_settings.py
├── advanced_anti_detection.py
├── account_manager.py
├── utils.py
├── database/
├── cursor-pro-vue/
└── ... (30+ 个散乱的Python文件)
```

### 新结构 (重构后)
```
cursor-pro/
├── src/cursor_pro/          # 主包
│   ├── __init__.py
│   ├── core/               # 核心功能
│   │   ├── main.py
│   │   ├── api_server.py
│   │   └── account_info.py
│   ├── auth/               # 认证模块
│   │   ├── cursor_auth.py
│   │   ├── oauth_auth.py
│   │   ├── token_manager.py
│   │   ├── signup.py
│   │   └── register.py
│   ├── backup/             # 备份模块
│   │   ├── settings.py
│   │   └── session.py
│   ├── anti_detection/     # 反检测模块
│   │   ├── advanced.py
│   │   ├── integration.py
│   │   ├── core.py
│   │   ├── bypass_token.py
│   │   ├── bypass_version.py
│   │   └── machine_reset.py
│   ├── management/         # 管理模块
│   │   ├── account_manager.py
│   │   ├── admin_panel.py
│   │   ├── config.py
│   │   ├── data_path.py
│   │   └── legacy_adapter.py
│   ├── utils/              # 工具模块
│   │   ├── common.py
│   │   ├── app_version.py
│   │   ├── version_control.py
│   │   ├── tuning.py
│   │   ├── updater.py
│   │   └── auth_check.py
│   └── database/           # 数据库模块
│       ├── config.py
│       └── sqlite_config.py
├── frontend/               # 前端项目 (原cursor-pro-vue)
├── scripts/                # 构建脚本
├── tests/                  # 测试目录
├── docs/                   # 文档目录
├── setup.py                # 安装脚本
├── pyproject.toml          # 项目配置
└── requirements.txt        # 依赖文件
```

## 📦 文件映射表

| 旧文件名 | 新位置 | 说明 |
|---------|--------|------|
| `main.py` | `src/cursor_pro/core/main.py` | 主程序入口 |
| `vue_with_api_optimized.py` | `src/cursor_pro/core/api_server.py` | API服务器 |
| `cursor_acc_info.py` | `src/cursor_pro/core/account_info.py` | 账户信息 |
| `cursor_auth.py` | `src/cursor_pro/auth/cursor_auth.py` | Cursor认证 |
| `oauth_auth.py` | `src/cursor_pro/auth/oauth_auth.py` | OAuth认证 |
| `get_user_token.py` | `src/cursor_pro/auth/token_manager.py` | Token管理 |
| `new_signup.py` | `src/cursor_pro/auth/signup.py` | 注册功能 |
| `cursor_register_manual.py` | `src/cursor_pro/auth/register.py` | 手动注册 |
| `backup_settings.py` | `src/cursor_pro/backup/settings.py` | 设置备份 |
| `backup_session.py` | `src/cursor_pro/backup/session.py` | 会话备份 |
| `advanced_anti_detection.py` | `src/cursor_pro/anti_detection/advanced.py` | 高级反检测 |
| `anti_detection_integration.py` | `src/cursor_pro/anti_detection/integration.py` | 反检测集成 |
| `cursor_pro_anti_detection.py` | `src/cursor_pro/anti_detection/core.py` | 核心反检测 |
| `bypass_token_limit.py` | `src/cursor_pro/anti_detection/bypass_token.py` | Token绕过 |
| `bypass_version.py` | `src/cursor_pro/anti_detection/bypass_version.py` | 版本绕过 |
| `reset_machine_manual.py` | `src/cursor_pro/anti_detection/machine_reset.py` | 机器ID重置 |
| `account_manager.py` | `src/cursor_pro/management/account_manager.py` | 账户管理器 |
| `admin_panel.py` | `src/cursor_pro/management/admin_panel.py` | 管理面板 |
| `config.py` | `src/cursor_pro/management/config.py` | 配置管理 |
| `data_path_manager.py` | `src/cursor_pro/management/data_path.py` | 数据路径管理 |
| `legacy_path_adapter.py` | `src/cursor_pro/management/legacy_adapter.py` | 遗留适配器 |
| `utils.py` | `src/cursor_pro/utils/common.py` | 通用工具 |
| `app_version.py` | `src/cursor_pro/utils/app_version.py` | 应用版本管理 |
| `version_control.py` | `src/cursor_pro/utils/version_control.py` | 版本控制 |
| `parameter_tuning.py` | `src/cursor_pro/utils/tuning.py` | 参数调优 |
| `disable_auto_update.py` | `src/cursor_pro/utils/updater.py` | 更新器 |
| `check_user_authorized.py` | `src/cursor_pro/utils/auth_check.py` | 认证检查 |
| `cursor-pro-vue/` | `frontend/cursor-pro-vue/` | 前端项目 |

## 🔄 导入语句变化

### 旧导入方式
```python
from main import some_function
import cursor_auth
from backup_settings import BackupManager
```

### 新导入方式
```python
from cursor_pro.core.main import some_function
import cursor_pro.auth.cursor_auth as cursor_auth
from cursor_pro.backup.settings import BackupManager
```

## 🚀 启动方式变化

### 旧启动方式
```bash
python main.py
python vue_with_api_optimized.py
```

### 新启动方式
```bash
# 方式1: 直接运行模块
python -m cursor_pro.core.main
python -m cursor_pro.core.api_server

# 方式2: 使用新的启动脚本
start-new.bat

# 方式3: 安装后使用命令行工具
pip install -e .
cursor-pro
cursor-pro-api
```

## 📋 重构优势

1. **标准化结构**: 遵循Python包开发最佳实践
2. **模块化设计**: 功能按模块清晰分离
3. **易于维护**: 代码组织更加清晰
4. **可扩展性**: 便于添加新功能模块
5. **包管理**: 支持标准的pip安装和分发
6. **测试友好**: 便于编写和运行单元测试
7. **文档完善**: 标准化的文档结构

## ⚠️ 注意事项

1. **导入路径**: 所有导入语句已自动更新，但如果有自定义脚本，需要手动更新
2. **配置文件**: 配置文件路径保持不变，向后兼容
3. **数据库**: 数据库文件和结构保持不变
4. **前端**: 前端项目移动到`frontend/`目录，但功能不变

## 🔧 故障排除

如果遇到导入错误，请：

1. 确保使用正确的导入路径
2. 设置PYTHONPATH: `set PYTHONPATH=%cd%\src` (Windows) 或 `export PYTHONPATH=$PWD/src` (Linux/Mac)
3. 或者安装开发版本: `pip install -e .`

## 📞 支持

如果在迁移过程中遇到问题，请查看：
- [README.md](README.md) - 基本使用说明
- [docs/](docs/) - 详细文档
- 或提交Issue到项目仓库
