# Cursor Pro API - Vercel部署

## 🚀 API地址
- **主域名：** `https://cursorpro-api.vercel.app`
- **版本检查API：** `https://cursorpro-api.vercel.app/api/check-version`
- **通用API：** `https://cursorpro-api.vercel.app/api/`

## 📋 API端点说明

### 1. 版本检查绕过 `/api/check-version`
- **用途：** 绕过Cursor的版本检查
- **方法：** GET/POST
- **返回：** 始终返回无需更新的响应

### 2. 通用API `/api/`
- **用途：** 处理所有其他请求
- **方法：** GET/POST
- **返回：** API状态和基本信息

## 🔧 使用方法

### 在Cursor中使用：
1. 修改hosts文件或使用代理
2. 将Cursor的更新检查请求重定向到您的API
3. 或者在您的本地程序中使用此API

### 测试API：
```bash
# 测试版本检查API
curl https://cursorpro-api.vercel.app/api/check-version

# 测试通用API
curl https://cursorpro-api.vercel.app/api/
```

## 📊 API响应示例

### 版本检查响应：
```json
{
  "hasUpdate": false,
  "version": "0.42.4",
  "releaseNotes": "No updates available - bypassed by cursor-pro",
  "downloadUrl": "",
  "forceUpdate": false,
  "timestamp": "2024-01-20T10:30:00.000Z",
  "status": "success",
  "message": "Version check bypassed successfully"
}
```

## 🛠️ 部署状态
- ✅ Vercel部署成功
- ✅ API端点已配置
- ✅ CORS已启用
- ✅ 支持GET/POST请求

## 📝 注意事项
- API支持跨域请求(CORS)
- 所有请求都会被记录到Vercel日志中
- API响应时间通常在100ms以内
- 支持HTTPS安全连接

## 🔄 更新API
要更新API代码：
1. 修改 `api/` 文件夹中的文件
2. 提交到GitHub
3. Vercel会自动重新部署

---
**创建时间：** 2024年1月20日  
**作者：** tul345  
**项目：** cursor-pro
