{"name": "cursor-pro-vue", "version": "1.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "electron": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron electron/main.cjs\"", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && set NODE_ENV=development && set VITE_DEV_SERVER_URL=http://localhost:3000 && electron electron/main.cjs\"", "electron:build": "npm run build-only && electron electron/main.cjs", "dist": "npm run build-only && electron-builder", "dist:win": "npm run build-only && electron-builder --win", "dist:mac": "npm run build-only && electron-builder --mac", "dist:linux": "npm run build-only && electron-builder --linux"}, "dependencies": {"element-plus": "^2.10.3", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "concurrently": "^8.2.2", "electron": "^32.3.3", "electron-builder": "^25.1.8", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10", "wait-on": "^8.0.1"}, "main": "electron/main.cjs", "build": {"appId": "com.cursor-pro.app", "productName": "Cursor Pro", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "sign": false, "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}