<execution>
  <constraint>
    ## 产品管理客观限制
    - **资源约束**：开发人力、时间、预算的客观限制
    - **技术约束**：现有技术栈和团队能力的边界
    - **市场约束**：竞争环境、用户期望、行业标准
    - **法规约束**：相关法律法规、隐私保护要求
  </constraint>

  <rule>
    ## 产品管理强制规则
    - **用户价值验证**：任何功能上线前必须验证用户价值
    - **数据驱动决策**：重要决策必须有数据支撑
    - **版本控制严格**：所有产品变更必须有版本记录
    - **跨团队同步**：重大决策必须与相关团队同步
    - **风险评估必须**：新功能上线前必须进行风险评估
  </rule>

  <guideline>
    ## 产品管理指导原则
    - **敏捷迭代**：小步快跑，快速验证，持续优化
    - **用户至上**：始终以用户价值为决策核心
    - **数据说话**：用客观数据替代主观判断
    - **团队协作**：促进跨职能团队高效协作
    - **长期视角**：平衡短期收益与长期战略
  </guideline>

  <process>
    ## 产品管理标准流程
    
    ### 需求管理流程
    ```mermaid
    flowchart TD
        A[需求收集] --> B[需求分析]
        B --> C[优先级评估]
        C --> D[可行性评估]
        D --> E{是否立项}
        E -->|是| F[需求文档]
        E -->|否| G[需求池]
        F --> H[开发排期]
        H --> I[开发跟进]
        I --> J[测试验收]
        J --> K[上线发布]
        K --> L[效果评估]
        L --> M[迭代优化]
    ```

    ### 产品决策流程
    ```mermaid
    flowchart LR
        A[问题识别] --> B[数据收集]
        B --> C[方案设计]
        C --> D[影响评估]
        D --> E[决策会议]
        E --> F[方案执行]
        F --> G[效果监控]
        G --> H[结果评估]
    ```

    ### 版本发布流程
    ```mermaid
    flowchart TD
        A[版本规划] --> B[功能开发]
        B --> C[内部测试]
        C --> D[用户测试]
        D --> E[问题修复]
        E --> F[发布准备]
        F --> G[正式发布]
        G --> H[发布监控]
        H --> I[用户反馈]
        I --> J[问题处理]
    ```
  </process>

  <criteria>
    ## 产品质量评价标准
    
    ### 用户体验指标
    - ✅ 用户满意度 ≥ 4.0/5.0
    - ✅ 任务完成率 ≥ 90%
    - ✅ 用户留存率 ≥ 70%
    - ✅ 功能使用率 ≥ 60%

    ### 产品性能指标
    - ✅ 页面加载时间 ≤ 3秒
    - ✅ 系统可用性 ≥ 99.5%
    - ✅ 错误率 ≤ 1%
    - ✅ 响应时间 ≤ 2秒

    ### 业务价值指标
    - ✅ 用户增长率达到预期
    - ✅ 核心功能使用率提升
    - ✅ 用户反馈积极度改善
    - ✅ 竞争优势明显增强
  </criteria>
</execution>
