import os
import shutil
import json
import datetime
import platform
from colorama import Fore, Style, init
from config import get_config

# 初始化colorama
init()

# 定义emoji常量
EMOJI = {
    'BACKUP': '💾',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'INFO': 'ℹ️',
    'FOLDER': '📁',
    'FILE': '📄',
    'WARNING': '⚠️',
    'SESSION': '🔄'
}

def backup_conversation_only(backup_name=None, translator=None):
    """快速备份Cursor对话记录（仅对话数据）"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 开始快速备份Cursor对话记录...{Style.RESET_ALL}")

        # 确定备份目录 - 使用统一的数据目录
        try:
            from data_path_manager import DataPathManager
            path_manager = DataPathManager()
            backup_dir = str(path_manager.get_path('backups') / "session_backups")
        except:
            # 备用方案
            backup_dir = os.path.join(os.path.expandvars("%APPDATA%"), "cursor-pro", "backups", "session_backups")
        os.makedirs(backup_dir, exist_ok=True)

        # 如果没有提供备份名称，使用时间戳
        if not backup_name:
            backup_name = f"对话备份_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建备份子目录
        backup_path = os.path.join(backup_dir, backup_name)
        os.makedirs(backup_path, exist_ok=True)

        # Windows下的Cursor对话数据路径
        cursor_dir = os.path.expandvars("%APPDATA%\\Cursor")
        conversation_paths = []

        if os.path.exists(cursor_dir):
            # 对话相关的关键路径
            key_paths = [
                os.path.join(cursor_dir, "User", "workspaceStorage"),
                os.path.join(cursor_dir, "User", "globalStorage"),
                os.path.join(cursor_dir, "Local Storage"),
                os.path.join(cursor_dir, "Session Storage"),
            ]

            for path in key_paths:
                if os.path.exists(path):
                    conversation_paths.append(path)
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 找到对话数据: {os.path.basename(path)}{Style.RESET_ALL}")

        if not conversation_paths:
            print(f"{Fore.RED}{EMOJI['ERROR']} 未找到Cursor对话数据{Style.RESET_ALL}")
            return False

        # 快速备份对话数据
        backup_info = {"folders": [], "files": [], "timestamp": str(datetime.datetime.now()), "system": "Windows"}
        total_files = 0

        for path in conversation_paths:
            try:
                folder_name = os.path.basename(path)
                dest_path = os.path.join(backup_path, folder_name)

                print(f"{Fore.CYAN}{EMOJI['INFO']} 正在备份: {folder_name}...{Style.RESET_ALL}")

                # 直接复制整个文件夹
                if os.path.exists(dest_path):
                    shutil.rmtree(dest_path)

                shutil.copytree(path, dest_path)

                # 统计文件数
                files_count = sum([len(files) for r, d, files in os.walk(dest_path)])
                total_files += files_count

                backup_info["folders"].append({
                    "original_path": path,
                    "backup_path": dest_path,
                    "folder_name": folder_name,
                    "files_copied": files_count
                })

                print(f"{Fore.GREEN}{EMOJI['FOLDER']} 已备份: {folder_name} ({files_count} 个文件){Style.RESET_ALL}")

            except Exception as e:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 跳过 {os.path.basename(path)}: {str(e)}{Style.RESET_ALL}")

        # 保存备份元数据
        metadata_path = os.path.join(backup_path, "backup_info.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, indent=2, ensure_ascii=False)

        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 对话备份完成！共备份 {total_files} 个文件{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} 备份位置: {backup_path}{Style.RESET_ALL}")

        return True

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 备份对话失败: {str(e)}{Style.RESET_ALL}")
        return False

def backup_session(backup_name=None, translator=None):
    """备份Cursor会话数据"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 开始快速备份Cursor对话数据...{Style.RESET_ALL}")

        # 直接使用快速对话备份
        return backup_conversation_only(backup_name, translator)
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 备份失败: {str(e)}{Style.RESET_ALL}")
        return False

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup_session.error', error=str(e)) if translator else f'备份会话时出错: {str(e)}'}{Style.RESET_ALL}")
        return False

def list_session_backups(translator=None):
    """列出所有会话备份"""
    try:
        # 使用统一的数据目录
        try:
            from data_path_manager import DataPathManager
            path_manager = DataPathManager()
            backup_dir = str(path_manager.get_path('backups') / "session_backups")
        except:
            # 备用方案
            backup_dir = os.path.join(os.path.expandvars("%APPDATA%"), "cursor-pro", "backups", "session_backups")
        if not os.path.exists(backup_dir):
            print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('backup_session.no_backups') if translator else '没有找到会话备份'}{Style.RESET_ALL}")
            return []
        
        backups = [d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d))]
        if not backups:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('backup_session.no_backups') if translator else '没有找到会话备份'}{Style.RESET_ALL}")
            return []
        
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('backup_session.found_backups', count=len(backups)) if translator else f'找到 {len(backups)} 个会话备份:'}{Style.RESET_ALL}")
        
        backup_details = []
        for backup in backups:
            metadata_path = os.path.join(backup_dir, backup, "backup_info.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    # 计算总的文件数量（包括文件夹中的文件）
                    total_files = len(info.get("files", []))
                    folder_files = 0
                    for folder_info in info.get("folders", []):
                        folder_files += folder_info.get("files_copied", 0)

                    backup_details.append({
                        "name": backup,
                        "timestamp": info.get("timestamp", "Unknown"),
                        "folders": len(info.get("folders", [])),
                        "files": len(info.get("files", [])),
                        "total_files": total_files + folder_files,
                        "folder_files": folder_files,
                        "system": info.get("system", "Unknown")
                    })
                except:
                    backup_details.append({
                        "name": backup,
                        "timestamp": "Error reading metadata",
                        "folders": 0,
                        "files": 0,
                        "system": "Unknown"
                    })
        
        # 按时间排序
        backup_details.sort(key=lambda x: x["name"], reverse=True)
        
        for i, backup in enumerate(backup_details, 1):
            print(f"{Fore.CYAN}{i}. {backup['name']} - {backup['timestamp']} ({backup['folders']} folders, {backup['files']} files, {backup['system']}){Style.RESET_ALL}")
        
        return backup_details
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup_session.list_error', error=str(e)) if translator else f'列出会话备份时出错: {str(e)}'}{Style.RESET_ALL}")
        return []

def main(translator=None):
    """主函数"""
    return backup_session(translator=translator)

if __name__ == "__main__":
    main() 