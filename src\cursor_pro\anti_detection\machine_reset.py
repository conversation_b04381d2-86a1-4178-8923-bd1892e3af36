import os
import sys
import json
import uuid
import hashlib
import shutil
import sqlite3
import platform
import re
import tempfile
import glob
from colorama import Fore, Style, init
from typing import Tuple
import cursor_pro.management.config as configparser
import traceback
from cursor_pro.management.config import get_config
from datetime import datetime

# Initialize colorama
init()

# Define emoji constants
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "WARNING": "⚠️",
}

LOG_BUFFER = []
def log(msg):
    print(msg)
    LOG_BUFFER.append(str(msg))
    if len(LOG_BUFFER) > 500:
        LOG_BUFFER.pop(0)

def get_user_documents_path():
     """Get user Documents folder path"""
     if sys.platform == "win32":
         try:
             import winreg
             with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                 documents_path, _ = winreg.QueryValueEx(key, "Personal")
                 return documents_path
         except Exception as e:
             # fallback
             return os.path.join(os.path.expanduser("~"), "Documents")
     elif sys.platform == "darwin":
         return os.path.join(os.path.expanduser("~"), "Documents")
     else:  # Linux
         # Get actual user's home directory
         sudo_user = os.environ.get('SUDO_USER')
         if sudo_user:
             return os.path.join("/home", sudo_user, "Documents")
         return os.path.join(os.path.expanduser("~"), "Documents")
     

def get_cursor_paths(translator=None) -> Tuple[str, str]:
    """ Get Cursor related paths"""
    system = platform.system()
    
    # Read config file
    config = configparser.ConfigParser()
    config_dir = os.path.join(get_user_documents_path(), ".cursor-pro")
    config_file = os.path.join(config_dir, "config.ini")
    
    # Create config directory if it doesn't exist
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
    
    # Default paths for different systems
    default_paths = {
        "Darwin": "/Applications/Cursor.app/Contents/Resources/app",
        "Windows": os.path.join(os.getenv("LOCALAPPDATA", ""), "Programs", "Cursor", "resources", "app"),
        "Linux": ["/opt/Cursor/resources/app", "/usr/share/cursor/resources/app", os.path.expanduser("~/.local/share/cursor/resources/app"), "/usr/lib/cursor/app/"]
    }
    
    if system == "Linux":
        # Look for extracted AppImage with correct usr structure
        extracted_usr_paths = glob.glob(os.path.expanduser("~/squashfs-root/usr/share/cursor/resources/app"))
        # Also check current directory for extraction without home path prefix
        current_dir_paths = glob.glob("squashfs-root/usr/share/cursor/resources/app")
        
        # Add any found paths to the Linux paths list
        default_paths["Linux"].extend(extracted_usr_paths)
        default_paths["Linux"].extend(current_dir_paths)
        
        # Print debug information
        print(f"{Fore.CYAN}{EMOJI['INFO']} Available paths found:{Style.RESET_ALL}")
        for path in default_paths["Linux"]:
            if os.path.exists(path):
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {path} (exists){Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} {path} (not found){Style.RESET_ALL}")
    
    
    # If config doesn't exist, create it with default paths
    if not os.path.exists(config_file):
        for section in ['MacPaths', 'WindowsPaths', 'LinuxPaths']:
            if not config.has_section(section):
                config.add_section(section)
        
        if system == "Darwin":
            config.set('MacPaths', 'cursor_path', default_paths["Darwin"])
        elif system == "Windows":
            config.set('WindowsPaths', 'cursor_path', default_paths["Windows"])
        elif system == "Linux":
            # For Linux, try to find the first existing path
            for path in default_paths["Linux"]:
                if os.path.exists(path):
                    config.set('LinuxPaths', 'cursor_path', path)
                    break
            else:
                # If no path exists, use the first one as default
                config.set('LinuxPaths', 'cursor_path', default_paths["Linux"][0])
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
    else:
        config.read(config_file, encoding='utf-8')
    
    # Get path based on system
    if system == "Darwin":
        section = 'MacPaths'
    elif system == "Windows":
        section = 'WindowsPaths'
    elif system == "Linux":
        section = 'LinuxPaths'
    else:
        raise OSError(translator.get('reset.unsupported_os', system=system) if translator else f"不支持的操作系统: {system}")
    
    if not config.has_section(section) or not config.has_option(section, 'cursor_path'):
        raise OSError(translator.get('reset.path_not_configured') if translator else "未配置 Cursor 路徑")
    
    base_path = config.get(section, 'cursor_path')
    
    # For Linux, try to find the first existing path if the configured one doesn't exist
    if system == "Linux" and not os.path.exists(base_path):
        for path in default_paths["Linux"]:
            if os.path.exists(path):
                base_path = path
                # Update config with the found path
                config.set(section, 'cursor_path', path)
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                break
    
    if not os.path.exists(base_path):
        raise OSError(translator.get('reset.path_not_found', path=base_path) if translator else f"找不到 Cursor 路徑: {base_path}")
    
    pkg_path = os.path.join(base_path, "package.json")
    main_path = os.path.join(base_path, "out/main.js")
    
    # Check if files exist
    if not os.path.exists(pkg_path):
        raise OSError(translator.get('reset.package_not_found', path=pkg_path) if translator else f"找不到 package.json: {pkg_path}")
    if not os.path.exists(main_path):
        raise OSError(translator.get('reset.main_not_found', path=main_path) if translator else f"找不到 main.js: {main_path}")
    
    return (pkg_path, main_path)

def get_cursor_machine_id_path(translator=None) -> str:
    """
    Get Cursor machineId file path based on operating system
    Returns:
        str: Path to machineId file
    """
    # Read configuration
    config_dir = os.path.join(get_user_documents_path(), ".cursor-pro")
    config_file = os.path.join(config_dir, "config.ini")
    config = configparser.ConfigParser()
    
    if os.path.exists(config_file):
        config.read(config_file)
    
    if sys.platform == "win32":  # Windows
        if not config.has_section('WindowsPaths'):
            config.add_section('WindowsPaths')
            config.set('WindowsPaths', 'machine_id_path', 
                os.path.join(os.getenv("APPDATA"), "Cursor", "machineId"))
        return config.get('WindowsPaths', 'machine_id_path')
        
    elif sys.platform == "linux":  # Linux
        if not config.has_section('LinuxPaths'):
            config.add_section('LinuxPaths')
            config.set('LinuxPaths', 'machine_id_path',
                os.path.expanduser("~/.config/cursor/machineid"))
        return config.get('LinuxPaths', 'machine_id_path')
        
    elif sys.platform == "darwin":  # macOS
        if not config.has_section('MacPaths'):
            config.add_section('MacPaths')
            config.set('MacPaths', 'machine_id_path',
                os.path.expanduser("~/Library/Application Support/Cursor/machineId"))
        return config.get('MacPaths', 'machine_id_path')
        
    else:
        raise OSError(f"Unsupported operating system: {sys.platform}")

    # Save any changes to config file
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)

def get_workbench_cursor_path(translator=None) -> str:
    """Get Cursor workbench.desktop.main.js path"""
    system = platform.system()

    # Read configuration
    config_dir = os.path.join(get_user_documents_path(), ".cursor-pro")
    config_file = os.path.join(config_dir, "config.ini")
    config = configparser.ConfigParser()

    if os.path.exists(config_file):
        config.read(config_file)
    
    paths_map = {
        "Darwin": {  # macOS
            "base": "/Applications/Cursor.app/Contents/Resources/app",
            "main": "out/vs/workbench/workbench.desktop.main.js"
        },
        "Windows": {
            "main": "out\\vs\\workbench\\workbench.desktop.main.js"
        },
        "Linux": {
            "bases": ["/opt/Cursor/resources/app", "/usr/share/cursor/resources/app", "/usr/lib/cursor/app/"],
            "main": "out/vs/workbench/workbench.desktop.main.js"
        }
    }
    
    if system == "Linux":
        # Add extracted AppImage with correct usr structure
        extracted_usr_paths = glob.glob(os.path.expanduser("~/squashfs-root/usr/share/cursor/resources/app"))
            
        paths_map["Linux"]["bases"].extend(extracted_usr_paths)

    if system not in paths_map:
        raise OSError(translator.get('reset.unsupported_os', system=system) if translator else f"不支持的操作系统: {system}")

    if system == "Linux":
        for base in paths_map["Linux"]["bases"]:
            main_path = os.path.join(base, paths_map["Linux"]["main"])
            print(f"{Fore.CYAN}{EMOJI['INFO']} Checking path: {main_path}{Style.RESET_ALL}")
            if os.path.exists(main_path):
                return main_path

    if system == "Windows":
        base_path = config.get('WindowsPaths', 'cursor_path')
    elif system == "Darwin":
        base_path = paths_map[system]["base"]
        if config.has_section('MacPaths') and config.has_option('MacPaths', 'cursor_path'):
            base_path = config.get('MacPaths', 'cursor_path')
    else:  # Linux
        # For Linux, we've already checked all bases in the loop above
        # If we're here, it means none of the bases worked, so we'll use the first one
        base_path = paths_map[system]["bases"][0]
        if config.has_section('LinuxPaths') and config.has_option('LinuxPaths', 'cursor_path'):
            base_path = config.get('LinuxPaths', 'cursor_path')

    main_path = os.path.join(base_path, paths_map[system]["main"])
    
    if not os.path.exists(main_path):
        raise OSError(translator.get('reset.file_not_found', path=main_path) if translator else f"未找到 Cursor main.js 文件: {main_path}")
        
    return main_path

def version_check(version: str, min_version: str = "", max_version: str = "", translator=None) -> bool:
    """Version number check"""
    version_pattern = r"^\d+\.\d+\.\d+$"
    try:
        if not re.match(version_pattern, version):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.invalid_version_format', version=version)}{Style.RESET_ALL}")
            return False

        def parse_version(ver: str) -> Tuple[int, ...]:
            return tuple(map(int, ver.split(".")))

        current = parse_version(version)

        if min_version and current < parse_version(min_version):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_too_low', version=version, min_version=min_version)}{Style.RESET_ALL}")
            return False

        if max_version and current > parse_version(max_version):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_too_high', version=version, max_version=max_version)}{Style.RESET_ALL}")
            return False

        return True

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_check_error', error=str(e))}{Style.RESET_ALL}")
        return False

def check_cursor_version(translator) -> bool:
    """Check Cursor version"""
    try:
        pkg_path, _ = get_cursor_paths(translator)
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('reset.reading_package_json', path=pkg_path)}{Style.RESET_ALL}")
        
        try:
            with open(pkg_path, "r", encoding="utf-8") as f:
                data = json.load(f)
        except UnicodeDecodeError:
            # If UTF-8 reading fails, try other encodings
            with open(pkg_path, "r", encoding="latin-1") as f:
                data = json.load(f)
                
        if not isinstance(data, dict):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.invalid_json_object')}{Style.RESET_ALL}")
            return False
            
        if "version" not in data:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.no_version_field')}{Style.RESET_ALL}")
            return False
            
        version = str(data["version"]).strip()
        if not version:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_field_empty')}{Style.RESET_ALL}")
            return False
            
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('reset.found_version', version=version)}{Style.RESET_ALL}")
        
        # Check version format
        if not re.match(r"^\d+\.\d+\.\d+$", version):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.invalid_version_format', version=version)}{Style.RESET_ALL}")
            return False
            
        # Compare versions
        try:
            current = tuple(map(int, version.split(".")))
            min_ver = (0, 45, 0)  # Use tuple directly instead of string
            
            if current >= min_ver:
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.version_check_passed', version=version, min_version='0.45.0')}{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('reset.version_too_low', version=version, min_version='0.45.0')}{Style.RESET_ALL}")
                return False
        except ValueError as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_parse_error', error=str(e))}{Style.RESET_ALL}")
            return False
            
    except FileNotFoundError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.package_not_found', path=pkg_path)}{Style.RESET_ALL}")
        return False
    except json.JSONDecodeError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.invalid_json_object')}{Style.RESET_ALL}")
        return False
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.check_version_failed', error=str(e))}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('reset.stack_trace')}: {traceback.format_exc()}{Style.RESET_ALL}")
        return False

def modify_workbench_js(file_path: str, translator=None) -> bool:
    """
    Modify file content
    """
    try:
        # Save original file permissions
        original_stat = os.stat(file_path)
        original_mode = original_stat.st_mode
        original_uid = original_stat.st_uid
        original_gid = original_stat.st_gid

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode="w", encoding="utf-8", errors="ignore", delete=False) as tmp_file:
            # Read original content
            with open(file_path, "r", encoding="utf-8", errors="ignore") as main_file:
                content = main_file.read()

            patterns = {
                # 通用按钮替换模式
                r'B(k,D(Ln,{title:"Upgrade to Pro",size:"small",get codicon(){return A.rocket},get onClick(){return t.pay}}),null)': r'B(k,D(Ln,{title:"yeongpin GitHub",size:"small",get codicon(){return A.github},get onClick(){return function(){window.open("https://github.com/yeongpin/cursor-pro","_blank")}}}),null)',
                
                # Windows/Linux/Mac 通用按钮替换模式
                r'M(x,I(as,{title:"Upgrade to Pro",size:"small",get codicon(){return $.rocket},get onClick(){return t.pay}}),null)': r'M(x,I(as,{title:"yeongpin GitHub",size:"small",get codicon(){return $.rocket},get onClick(){return function(){window.open("https://github.com/yeongpin/cursor-pro","_blank")}}}),null)',
                
                # Badge 替换
                r'<div>Pro Trial': r'<div>Pro',

                r'py-1">Auto-select': r'py-1">Bypass-Version-Pin',
                
                #
                r'async getEffectiveTokenLimit(e){const n=e.modelName;if(!n)return 2e5;':r'async getEffectiveTokenLimit(e){return 9000000;const n=e.modelName;if(!n)return 9e5;',
                # Pro
                r'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>.");': r'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>. <h1>Pro</h1>");',
                
                # Toast 替换
                r'notifications-toasts': r'notifications-toasts hidden'
            }

            # 使用patterns进行替换
            for old_pattern, new_pattern in patterns.items():
                content = content.replace(old_pattern, new_pattern)

            # Write to temporary file
            tmp_file.write(content)
            tmp_path = tmp_file.name

        # Backup original file with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{file_path}.backup.{timestamp}"
        shutil.copy2(file_path, backup_path)
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.backup_created', path=backup_path)}{Style.RESET_ALL}")
        
        # Move temporary file to original position
        if os.path.exists(file_path):
            os.remove(file_path)
        shutil.move(tmp_path, file_path)

        # Restore original permissions
        os.chmod(file_path, original_mode)
        if os.name != "nt":  # Not Windows
            os.chown(file_path, original_uid, original_gid)

        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.file_modified')}{Style.RESET_ALL}")
        return True

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.modify_file_failed', error=str(e))}{Style.RESET_ALL}")
        if "tmp_path" in locals():
            try:
                os.unlink(tmp_path)
            except:
                pass
        return False

def modify_main_js(main_path: str, translator) -> bool:
    """Modify main.js file"""
    try:
        original_stat = os.stat(main_path)
        original_mode = original_stat.st_mode
        original_uid = original_stat.st_uid
        original_gid = original_stat.st_gid

        with tempfile.NamedTemporaryFile(mode="w", delete=False) as tmp_file:
            with open(main_path, "r", encoding="utf-8") as main_file:
                content = main_file.read()

            patterns = {
                r"async getMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMachineId(){return \1}",
                r"async getMacMachineId\(\)\{return [^??]+\?\?([^}]+)\}": r"async getMacMachineId(){return \1}",
            }

            for pattern, replacement in patterns.items():
                content = re.sub(pattern, replacement, content)

            tmp_file.write(content)
            tmp_path = tmp_file.name

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{main_path}.old.{timestamp}"
        shutil.copy2(main_path, backup_path)
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.backup_created', path=backup_path)}{Style.RESET_ALL}")
        shutil.move(tmp_path, main_path)

        os.chmod(main_path, original_mode)
        if os.name != "nt":
            os.chown(main_path, original_uid, original_gid)

        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.file_modified')}{Style.RESET_ALL}")
        return True

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.modify_file_failed', error=str(e))}{Style.RESET_ALL}")
        if "tmp_path" in locals():
            os.unlink(tmp_path)
        return False

def patch_cursor_get_machine_id(translator) -> bool:
    """Patch Cursor getMachineId function"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('reset.start_patching')}...{Style.RESET_ALL}")
        
        # Get paths
        pkg_path, main_path = get_cursor_paths(translator)
        
        # Check file permissions
        for file_path in [pkg_path, main_path]:
            if not os.path.isfile(file_path):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.file_not_found', path=file_path)}{Style.RESET_ALL}")
                return False
            if not os.access(file_path, os.W_OK):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.no_write_permission', path=file_path)}{Style.RESET_ALL}")
                return False

        # Get version number
        try:
            with open(pkg_path, "r", encoding="utf-8") as f:
                version = json.load(f)["version"]
            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('reset.current_version', version=version)}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.read_version_failed', error=str(e))}{Style.RESET_ALL}")
            return False

        # Check version
        if not version_check(version, min_version="0.45.0", translator=translator):
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.version_not_supported')}{Style.RESET_ALL}")
            return False

        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('reset.version_check_passed')}{Style.RESET_ALL}")

        # Backup file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{main_path}.bak.{timestamp}"
        if not os.path.exists(backup_path):
            shutil.copy2(main_path, backup_path)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.backup_created', path=backup_path)}{Style.RESET_ALL}")

        # Modify file
        if not modify_main_js(main_path, translator):
            return False

        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.patch_completed')}{Style.RESET_ALL}")
        return True

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.patch_failed', error=str(e))}{Style.RESET_ALL}")
        return False

class MachineIDResetter:
    def __init__(self, translator):
        self.translator = translator
        self.cursor_paths = self._get_cursor_paths()
        self.machine_id_paths = self._get_machine_id_paths()
        self.debug = False
        
    def _get_cursor_paths(self):
        """获取Cursor路径"""
        paths = []
        try:
            if platform.system() == "Windows":
                # Windows路径
                appdata = os.environ.get("APPDATA")
                local_appdata = os.environ.get("LOCALAPPDATA")
                
                if appdata:
                    paths.append(os.path.join(appdata, "Cursor"))
                if local_appdata:
                    paths.append(os.path.join(local_appdata, "Cursor"))
                    
            elif platform.system() == "Darwin":
                # macOS路径
                home = os.path.expanduser("~")
                paths.append(os.path.join(home, "Library", "Application Support", "Cursor"))
                
            elif platform.system() == "Linux":
                # Linux路径
                home = os.path.expanduser("~")
                paths.append(os.path.join(home, ".config", "cursor"))
                
        except Exception as e:
            log(f"获取Cursor路径时出错: {str(e)}")
            
        return paths
        
    def _get_machine_id_paths(self):
        """获取机器ID文件路径"""
        paths = []
        
        for cursor_path in self.cursor_paths:
            # 主机器ID文件
            paths.append(os.path.join(cursor_path, "machineid"))
            
            # 其他可能的机器ID文件位置
            paths.append(os.path.join(cursor_path, "User", "machineid"))
            paths.append(os.path.join(cursor_path, "Cache", "machineid"))
            
        return paths
        
    def reset_machine_ids(self):
        """重置所有机器ID文件"""
        log("开始重置机器ID...")

        success_count = 0
        total_operations = 0

        for path in self.machine_id_paths:
            try:
                if os.path.exists(path):
                    total_operations += 1
                    # 备份原始文件
                    backup_path = f"{path}.backup"
                    try:
                        shutil.copy2(path, backup_path)
                        log(f"已备份机器ID文件: {path} -> {backup_path}")
                    except Exception as e:
                        log(f"备份机器ID文件失败: {str(e)}")

                    # 删除原始文件
                    os.remove(path)
                    log(f"已删除机器ID文件: {path}")
                    success_count += 1

            except Exception as e:
                log(f"处理机器ID文件时出错: {path}, {str(e)}")

        # 在Windows上，还需要重置注册表中的机器ID
        windows_success = True
        if platform.system() == "Windows":
            try:
                windows_success = self._reset_windows_machine_id()
                if windows_success:
                    log("Windows注册表机器ID重置成功")
                else:
                    log("Windows注册表机器ID重置部分失败")
            except Exception as e:
                log(f"Windows注册表机器ID重置失败: {str(e)}")
                windows_success = False

        log("机器ID重置完成")

        # 返回成功状态
        if total_operations == 0:
            log("未找到需要重置的机器ID文件")
            # 如果Windows注册表重置成功，仍然算作部分成功
            if windows_success:
                log("但Windows注册表机器ID重置成功")
                return True
            else:
                log("且Windows注册表机器ID重置也失败")
                return False

        overall_success = (success_count == total_operations) and windows_success
        log(f"重置结果: 成功处理 {success_count}/{total_operations} 个文件, Windows注册表: {'成功' if windows_success else '失败'}")
        return overall_success
        
    def _reset_windows_machine_id(self):
        """重置Windows注册表中的机器ID"""
        try:
            import winreg

            success_count = 0
            total_count = 0

            # 尝试重置注册表中的机器ID
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machineId"),
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machine-id"),
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor", "MachineGUID"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machineId"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machine-id"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor", "MachineGUID")
            ]

            for hkey, key_path, value_name in reg_paths:
                total_count += 1  # 先计入总数
                try:
                    key = winreg.OpenKey(hkey, key_path, 0, winreg.KEY_SET_VALUE)
                    winreg.DeleteValue(key, value_name)
                    winreg.CloseKey(key)
                    log(f"已删除注册表项: {key_path}\\{value_name}")
                    success_count += 1
                except Exception as e:
                    # 检查是否是找不到键的错误
                    error_msg = str(e).lower()
                    if "找不到" in str(e) or "not found" in error_msg or "cannot find" in error_msg:
                        log(f"注册表项不存在（跳过）: {key_path}\\{value_name}")
                        # 不存在的项也算"成功"处理
                        success_count += 1
                    else:
                        log(f"删除注册表项时出错: {key_path}\\{value_name}, {str(e)}")

            log(f"Windows注册表重置完成: 成功处理 {success_count}/{total_count} 个项")

            # 如果所有项都成功处理（包括不存在的），返回True
            return success_count == total_count

        except Exception as e:
            log(f"重置Windows注册表机器ID时出错: {str(e)}")
            return False

    def _update_windows_machine_guid(self):
        """更新Windows注册表中的MachineGUID"""
        if platform.system() != "Windows":
            return
        try:
            import winreg
            import uuid

            # 生成新的GUID
            new_guid = str(uuid.uuid4())
            log(f"生成新的MachineGUID: {new_guid}")

            # 尝试更新注册表中的MachineGUID
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor", "MachineGUID"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor", "MachineGUID")
            ]

            for hkey, key_path, value_name in reg_paths:
                try:
                    # 尝试创建键（如果不存在）
                    try:
                        key = winreg.CreateKeyEx(hkey, key_path, 0, winreg.KEY_WRITE)
                    except:
                        key = winreg.OpenKey(hkey, key_path, 0, winreg.KEY_WRITE)
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_guid)
                    winreg.CloseKey(key)
                    log(f"已更新注册表项: {key_path}\\{value_name} = {new_guid}")
                except Exception as e:
                    log(f"更新注册表项时出错: {key_path}\\{value_name}, {str(e)}")
        except Exception as e:
            log(f"更新Windows MachineGUID时出错: {str(e)}")

    def _update_windows_machine_id(self):
        """更新Windows注册表中的machineId"""
        if platform.system() != "Windows":
            return
            
        try:
            import winreg
            import uuid
            
            # 生成新的机器ID
            new_id = str(uuid.uuid4())
            log(f"生成新的machineId: {new_id}")
            
            # 尝试更新注册表中的machineId
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machineId"),
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machine-id"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machineId"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machine-id")
            ]
            
            for hkey, key_path, value_name in reg_paths:
                try:
                    # 尝试创建键（如果不存在）
                    try:
                        key = winreg.CreateKeyEx(hkey, key_path, 0, winreg.KEY_WRITE)
                    except:
                        key = winreg.OpenKey(hkey, key_path, 0, winreg.KEY_WRITE)
                        
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, new_id)
                    winreg.CloseKey(key)
                    log(f"已更新注册表项: {key_path}\\{value_name} = {new_id}")
                except Exception as e:
                    log(f"更新注册表项时出错: {key_path}\\{value_name}, {str(e)}")
                        
        except Exception as e:
            log(f"更新Windows machineId时出错: {str(e)}")
            
    def restore_machine_id(self, machine_id):
        """恢复指定的机器ID"""
        if not machine_id:
            log("未提供机器ID，无法恢复")
            return False

        log(f"开始恢复机器ID: {machine_id}")
        
        success = False
        for path in self.machine_id_paths:
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(path), exist_ok=True)
                
                # 写入新的机器ID
                with open(path, 'w') as f:
                    f.write(machine_id)
                    
                log(f"已恢复机器ID到文件: {path}")
                success = True
                
            except Exception as e:
                log(f"恢复机器ID到文件时出错: {path}, {str(e)}")
                
        # 在Windows上，还需要更新注册表中的机器ID
        if platform.system() == "Windows" and success:
            self._restore_windows_machine_id(machine_id)
            
        return success
        
    def _restore_windows_machine_id(self, machine_id):
        """恢复Windows注册表中的机器ID"""
        if platform.system() != "Windows":
            return
            
        try:
            import winreg
            
            log(f"开始恢复Windows注册表中的机器ID: {machine_id}")
            
            # 尝试更新注册表中的机器ID
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machineId"),
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor\Cursor", "machine-id"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machineId"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor\Cursor", "machine-id")
            ]
            
            for hkey, key_path, value_name in reg_paths:
                try:
                    # 尝试创建键（如果不存在）
                    try:
                        key = winreg.CreateKeyEx(hkey, key_path, 0, winreg.KEY_WRITE)
                    except:
                        key = winreg.OpenKey(hkey, key_path, 0, winreg.KEY_WRITE)
                        
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, machine_id)
                    winreg.CloseKey(key)
                    log(f"已更新注册表项: {key_path}\\{value_name} = {machine_id}")
                except Exception as e:
                    log(f"更新注册表项时出错: {key_path}\\{value_name}, {str(e)}")
                    
            # 同时更新MachineGUID，使用相同的值
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Cursor", "MachineGUID"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor", "MachineGUID")
            ]
            
            for hkey, key_path, value_name in reg_paths:
                try:
                    # 尝试创建键（如果不存在）
                    try:
                        key = winreg.CreateKeyEx(hkey, key_path, 0, winreg.KEY_WRITE)
                    except:
                        key = winreg.OpenKey(hkey, key_path, 0, winreg.KEY_WRITE)
                        
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, machine_id)
                    winreg.CloseKey(key)
                    log(f"已更新注册表项: {key_path}\\{value_name} = {machine_id}")
                except Exception as e:
                    log(f"更新注册表项时出错: {key_path}\\{value_name}, {str(e)}")

        except Exception as e:
            log(f"恢复Windows注册表机器ID时出错: {str(e)}")

def get_machine_id():
    try:
        machine_id_path = get_cursor_machine_id_path()
        log(f"[日志] machineId 路径: {machine_id_path}")
        if os.path.exists(machine_id_path):
            with open(machine_id_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                log(f"[日志] machineId 内容: {content}")
                return content or "machineId内容为空"
        else:
            log("[日志] 未找到 machineId 文件")
            return "未找到 machineId 文件"
    except Exception as e:
        log(f"[日志] 读取 machineId 失败: {str(e)}")
        return "读取 machineId 失败"

def run(translator=None):
    config = get_config(translator)
    if not config:
        return False
    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['RESET']} {translator.get('reset.title')}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")

    resetter = MachineIDResetter(translator)  # Correctly pass translator
    resetter.reset_machine_ids()

    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    input(f"{EMOJI['INFO']} {translator.get('reset.press_enter')}...")

def reset_machine_id():
    try:
        log("[日志] 开始重置机器ID...")
        # 获取translator
        try:
            from cursor_pro.core.main import translator as main_translator
            translator = main_translator
        except:
            translator = None

        resetter = MachineIDResetter(translator)
        result = resetter.reset_machine_ids()
        log(f"[日志] 重置机器ID结果: {result}")
        return "重置机器ID成功" if result else "重置机器ID失败"
    except Exception as e:
        log(f"[日志] 重置机器ID失败: {str(e)}")
        return f"重置机器ID失败: {str(e)}"

def generate_machine_id():
    try:
        log("[日志] 开始生成机器ID...")
        # 获取translator
        try:
            from cursor_pro.core.main import translator as main_translator
            translator = main_translator
        except:
            translator = None

        resetter = MachineIDResetter(translator)
        result = resetter.reset_machine_ids()
        log(f"[日志] 生成机器ID结果: {result}")
        return "生成机器ID成功" if result else "生成机器ID失败"
    except Exception as e:
        log(f"[日志] 生成机器ID失败: {str(e)}")
        return f"生成机器ID失败: {str(e)}"

def restore_machine_id():
    log("[日志] 恢复机器ID功能待实现")
    return "恢复机器ID功能待实现"

def reset_config():
    """重置配置 - 实际上是重置机器ID"""
    try:
        log("[日志] 开始重置机器码...")
        # 获取translator
        try:
            from cursor_pro.core.main import translator as main_translator
            translator = main_translator
        except:
            translator = None

        resetter = MachineIDResetter(translator)
        result = resetter.reset_machine_ids()
        log(f"[日志] 重置机器码结果: {result}")
        return "重置机器码成功" if result else "重置机器码失败"
    except Exception as e:
        log(f"[日志] 重置机器码失败: {str(e)}")
        return f"重置机器码失败: {str(e)}"

def get_log():
    return '\n'.join(LOG_BUFFER)

if __name__ == "__main__":
    from cursor_pro.core.main import translator as main_translator
    run(main_translator)
