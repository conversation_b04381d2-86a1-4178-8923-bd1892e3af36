# Cursor Pro 数据存储统一指南

## 概述

本项目已完成数据存储架构统一，所有数据（包括SQLite数据库和配置文件）都存储在标准的用户数据目录中，解决了之前的双重存储问题，提高了数据安全性和软件专业性。

## ✅ 统一完成状态

- **SQLite数据库**: 已统一到 `%APPDATA%/cursor-pro/data/cursor_pro.db`
- **配置文件**: 统一到 `%APPDATA%/cursor-pro/config/`
- **日志文件**: 统一到 `%APPDATA%/cursor-pro/logs/`
- **备份文件**: 统一到 `%APPDATA%/cursor-pro/backups/`
- **所有模块**: 使用统一的路径管理器

## 新的目录结构

### Windows 系统
```
%APPDATA%\cursor-pro\
├── data\                    # 用户数据文件
│   ├── cursor_accounts.txt
│   ├── cursor_accounts_backup.txt
│   ├── cursor_pro.db
│   └── block_domain.txt
├── config\                  # 配置文件
│   ├── config.ini
│   ├── settings.json
│   └── register_config.json
├── logs\                    # 日志文件
│   ├── app.log
│   └── migration_report_*.json
├── backups\                 # 备份文件
│   └── migration_backup_*/
├── temp\                    # 临时文件
└── cache\                   # 缓存文件
```

### macOS 系统
```
~/Library/Application Support/cursor-pro/
```

### Linux 系统
```
~/.config/cursor-pro/
```

## 迁移步骤

### 1. 自动迁移（推荐）

运行数据迁移工具：
```bash
python data_migration.py
```

该工具会：
- 自动检测需要迁移的文件
- 创建迁移前备份
- 将文件复制到新位置
- 生成迁移报告

### 2. 手动迁移

如果需要手动迁移，请按以下步骤：

1. 创建用户数据目录
2. 将以下文件移动到对应位置：
   - `cursor_accounts.txt` → `data/`
   - `cursor_accounts_backup.txt` → `data/`
   - `cursor_pro.db` → `data/`
   - `config.ini` → `config/`
   - 等等...

## 代码修改指南

### 阶段一：使用遗留路径适配器（最小修改）

```python
# 旧代码
accounts_file = "cursor_accounts.txt"
with open(accounts_file, 'r') as f:
    content = f.read()

# 新代码
from legacy_path_adapter import get_legacy_adapter
adapter = get_legacy_adapter()
accounts_file = adapter.get_accounts_file()
content = accounts_file.read_text(encoding='utf-8')
```

### 阶段二：使用便捷函数（简化操作）

```python
# 旧代码
import os
accounts_file = "cursor_accounts.txt"
if os.path.exists(accounts_file):
    with open(accounts_file, 'r') as f:
        content = f.read()

# 新代码
from legacy_path_adapter import read_file, file_exists
if file_exists('cursor_accounts.txt'):
    content = read_file('cursor_accounts.txt')
```

### 阶段三：完全使用新路径管理器（推荐）

```python
# 新代码
from data_path_manager import get_data_path_manager
path_manager = get_data_path_manager()
accounts_file = path_manager.get_data_file('cursor_accounts.txt')
if accounts_file.exists():
    content = accounts_file.read_text(encoding='utf-8')
```

## 主要优势

### 1. 符合软件标准
- 遵循操作系统规范
- 数据和代码分离
- 支持多用户环境

### 2. 提高数据安全
- 避免意外删除
- 权限管理更好
- 备份更容易

### 3. 改善用户体验
- 升级不影响数据
- 卸载保留用户数据
- 数据位置更直观

### 4. 开发维护友好
- Git不会跟踪用户数据
- 部署更简单
- 测试更方便

## 向后兼容性

### 自动检测机制
新系统会自动检测文件位置：
1. 优先使用新位置的文件
2. 如果新位置不存在，检查旧位置
3. 提示用户进行迁移

### 渐进式迁移
- 可以逐步迁移代码
- 新旧系统可以并存
- 不会破坏现有功能

## 工具和脚本

### 1. 数据路径管理器 (`data_path_manager.py`)
- 统一管理所有数据路径
- 自动创建目录结构
- 跨平台支持

### 2. 数据迁移工具 (`data_migration.py`)
- 自动迁移现有数据
- 创建备份
- 生成迁移报告

### 3. 遗留路径适配器 (`legacy_path_adapter.py`)
- 向后兼容支持
- 简化代码修改
- 提供便捷函数

### 4. 代码迁移示例 (`code_migration_example.py`)
- 展示迁移方法
- 提供最佳实践
- 分阶段迁移指导

## 常见问题

### Q: 迁移后原来的文件怎么办？
A: 迁移工具会自动创建备份，原文件可以在确认迁移成功后手动删除。

### Q: 如何回滚迁移？
A: 使用迁移工具的回滚功能：
```python
from data_migration import DataMigration
migration = DataMigration()
migration.rollback_migration('备份目录路径')
```

### Q: 新用户安装后数据在哪里？
A: 新用户的数据会自动创建在标准用户数据目录中，不会污染项目目录。

### Q: 如何查看当前数据位置？
A: 运行路径管理器：
```bash
python data_path_manager.py
```

## 最佳实践

### 1. 开发阶段
- 使用遗留适配器进行渐进式迁移
- 充分测试新旧路径兼容性
- 保持代码简洁

### 2. 部署阶段
- 确保.gitignore包含用户数据文件
- 测试安装包的路径配置
- 提供迁移工具给用户

### 3. 维护阶段
- 定期清理临时文件
- 监控数据目录大小
- 提供数据备份功能

## 技术细节

### 路径解析优先级
1. 新位置（用户数据目录）
2. 旧位置（项目目录）
3. 创建新文件（默认新位置）

### 文件类型映射
- `.txt` 数据文件 → `data/`
- `.ini`, `.json` 配置文件 → `config/`
- `.log` 日志文件 → `logs/`
- `backup*`, `*.bak` 备份文件 → `backups/`

### 错误处理
- 权限不足时的降级处理
- 磁盘空间不足的检测
- 文件锁定的重试机制

---

**注意**: 建议在生产环境部署前，先在测试环境完整验证迁移流程。
