# 🚀 群发布版本控制使用说明

## 📋 系统功能

这个系统让你能够：
- ✅ **群发布管理** - 发布新版本到群里，旧版本自动失效
- ✅ **强制更新** - 用户重新打开软件时必须更新
- ✅ **自定义提示** - 自定义更新说明和群信息
- ✅ **用户统计** - 查看有多少人在使用你的软件

## 🎯 使用流程

### 1. 日常开发流程

```
修复bug/添加功能 → 打包新版本 → 发到群里 → 在网站发布新版本 → 用户被强制更新
```

### 2. 具体操作步骤

#### 步骤1：开发和打包
```bash
# 1. 修改代码，修复bug或添加功能
# 2. 更新版本号（.env文件）
version=1.0.2

# 3. 打包新版本
python build_pyinstaller.py
```

#### 步骤2：发布到群里
- 将打包好的软件发到QQ群/微信群
- 告诉群友有新版本可以下载

#### 步骤3：在管理网站发布
```bash
# 运行管理面板
python simple_admin.py
```

在管理面板中：
1. 输入新版本号（如：1.0.2）
2. 填写更新说明（如：修复了XX bug，添加了XX功能）
3. 确认群信息（如：QQ群：123456789）
4. 点击"🚀 发布新版本（强制更新）"

#### 步骤4：用户自动更新
- 用户重新打开软件时会看到更新弹窗
- 弹窗显示：需要更新，请到群里下载
- 用户必须下载新版本才能继续使用

## 🖥️ 管理面板功能

### 主要功能

1. **发布新版本**
   - 输入版本号和更新说明
   - 一键发布，所有旧版本立即失效

2. **查看用户统计**
   - 看有多少人在使用
   - 各版本的用户分布

3. **允许所有版本**
   - 紧急情况下取消强制更新
   - 让用户可以继续使用旧版本

4. **测试连接**
   - 检查API服务器是否正常

### 界面预览

```
🚀 Cursor Pro 群发布管理

┌─ 当前状态 ─────────────────────────┐
│ 当前最新版本: 1.0.1 | 强制更新: 是 │
│ 总用户数: 156                      │
└────────────────────────────────────┘

┌─ 发布新版本 ───────────────────────┐
│ 新版本号: [1.0.2        ]          │
│ 更新说明: [修复了重要bug...]        │
│ 群信息:   [QQ群：123456789]        │
│                                    │
│     🚀 发布新版本（强制更新）       │
└────────────────────────────────────┘

┌─ 快速操作 ─────────────────────────┐
│ [允许所有版本] [查看用户统计] [测试连接] │
└────────────────────────────────────┘
```

## 🔧 部署配置

### 1. 部署API服务器（一次性）

使用免费的Vercel部署：

```bash
# 1. 安装Vercel CLI
npm install -g vercel

# 2. 登录Vercel
vercel login

# 3. 部署API服务器
vercel --prod
```

### 2. 配置API地址

在 `version_control.py` 和 `simple_admin.py` 中修改：
```python
self.api_base = "https://your-app.vercel.app/api/v1"  # 替换为你的域名
```

### 3. 设置管理员密码

在 `version_api_server.py` 中设置：
```python
if auth_token != 'Bearer your-secure-password-here':  # 设置你的密码
```

在 `simple_admin.py` 中设置相同密码：
```python
self.admin_token = "your-secure-password-here"  # 设置相同密码
```

## 📱 用户看到的效果

### 更新弹窗示例

```
┌─────────────────────────────────────┐
│           🚀 发现新版本！            │
│                                     │
│    当前版本: 1.0.1                  │
│    最新版本: 1.0.2                  │
│                                     │
│    修复了重要bug，添加了新功能       │
│                                     │
│ ⚠️ 检测到新版本，请到群里下载最新版本 │
│    才能继续使用                     │
│                                     │
│    请到QQ群/微信群下载最新版本       │
│                                     │
│         [去群里下载]                │
└─────────────────────────────────────┘
```

## 💡 使用技巧

### 1. 版本号规范
- 使用语义化版本号：`主版本.次版本.修订版本`
- 例如：1.0.0 → 1.0.1 → 1.1.0 → 2.0.0

### 2. 更新说明建议
- 简洁明了，突出重点
- 例如："修复了XX bug，添加了XX功能，提升了性能"

### 3. 群管理建议
- 在群里置顶下载链接
- 及时回应用户问题
- 定期清理旧版本文件

### 4. 紧急情况处理
- 如果新版本有问题，立即点击"允许所有版本"
- 修复问题后重新发布

## 🔒 安全注意事项

1. **保护管理员密码**
   - 使用强密码
   - 不要分享给他人

2. **定期备份**
   - 备份用户统计数据
   - 保存重要配置

3. **监控使用情况**
   - 定期查看用户统计
   - 关注异常情况

## 🆘 常见问题

### Q: 用户说看不到更新弹窗？
A: 检查用户是否联网，或者API服务器是否正常

### Q: 如何取消强制更新？
A: 在管理面板点击"允许所有版本"

### Q: 用户统计不准确？
A: 统计基于用户设备指纹，同一设备只计算一次

### Q: API服务器挂了怎么办？
A: 软件有3天离线宽限期，期间用户可以正常使用

---

**这个系统让你完全掌控软件版本，既保护了你的权益，又提供了良好的用户体验！** 🎉

有问题随时联系技术支持！
