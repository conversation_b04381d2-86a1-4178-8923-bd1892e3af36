@echo off
title Cursor Pro - Complete Installation
color 0A

echo ========================================
echo  Cursor Pro - Complete Installation
echo  Enhanced Edition with Anti-Detection
echo ========================================
echo.

echo This installer will set up Cursor Pro on your system.
echo.
echo Components included:
echo [1] Python Backend Server
echo [2] Electron Desktop Application  
echo [3] Documentation and Guides
echo.

pause

echo.
echo [1/3] Installing Backend Server...
echo.

REM 创建安装目录（用户目录，避免权限问题）
set INSTALL_DIR=%USERPROFILE%\Cursor Pro
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

REM 复制后端文件
xcopy /E /I /Y "backend\*" "%INSTALL_DIR%\backend\"
echo ✓ Backend server installed

echo.
echo [2/3] Installing Desktop Application...
echo.

REM 安装前端应用
cd frontend\dist
for %%f in (*.exe) do (
    echo Installing %%f...
    start /wait "" "%%f"
)
cd ..\..

echo ✓ Desktop application installed

echo.
echo [3/3] Setting up shortcuts...
echo.

REM 创建桌面快捷方式
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Cursor Pro.lnk'); $Shortcut.TargetPath = '%USERPROFILE%\Cursor Pro\backend\cursor-backend.exe'; $Shortcut.Save()"

echo ✓ Desktop shortcut created

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo Cursor Pro has been successfully installed.
echo.
echo To start using Cursor Pro:
echo 1. Double-click the desktop shortcut, or
echo 2. Run from Start Menu
echo.
echo For documentation, see the 'docs' folder.
echo.
echo Thank you for using Cursor Pro!
echo.
pause
