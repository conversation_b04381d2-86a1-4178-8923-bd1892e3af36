Cursor Pro Portable Edition - User Guide
========================================

File Structure:
├── Launch-Cursor-Pro.bat   # One-click launch script
├── backend/                # Backend service files
│   └── cursor-backend.exe  # Backend main program
├── frontend/               # Frontend application files
│   └── Cursor Pro.exe      # Frontend main program
└── README.txt              # This file

Quick Start:
1. Double-click "Launch-Cursor-Pro.bat" to start
2. Or run separately:
   - backend/cursor-backend.exe (Backend service)
   - frontend/Cursor Pro.exe (Frontend interface)

System Requirements:
- Windows 10/11 (64-bit)
- No additional dependencies required
- Recommended 4GB+ RAM

Port Configuration:
- Backend service default port: 8080
- Frontend automatically connects to backend service

Features:
- Cursor account management
- Automatic registration and login
- Anti-detection mechanisms
- Multi-account support
- Local data storage

Troubleshooting:
1. If startup fails, check:
   - Antivirus software blocking
   - Port 8080 availability
   - Sufficient disk space

2. If frontend cannot connect to backend:
   - Ensure backend service is running
   - Check firewall settings
   - Restart the application

Technical Support:
- Check console output for issues
- Keep error logs for troubleshooting

Important Notes:
- This software is for educational and research purposes only
- Please comply with relevant laws and regulations
- Read the disclaimer before use

Update Instructions:
- Portable version supports direct overwrite updates
- User data and configuration files are preserved

========================================
Version: v1.1.0 Portable Edition
Updated: 2025-07-28
========================================
