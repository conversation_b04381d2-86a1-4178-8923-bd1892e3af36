@echo off
title Cursor Pro

echo ========================================
echo Cursor Pro Launcher
echo ========================================
echo.

echo Checking Python...

REM Try different Python commands
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python3
    goto :python_found
)

py -3 --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py -3
    goto :python_found
)

echo ERROR: Python not found
echo Please install Python 3.7+ from https://python.org
echo Make sure to check "Add Python to PATH" during installation
pause
exit /b 1

:python_found

echo Python OK
echo.

echo Starting backend server...
start "Backend Server" %PYTHON_CMD% vue_with_api_optimized.py

echo Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo Starting frontend server...
cd cursor-pro-vue
start "Frontend Server" npm run dev
cd ..

echo Opening browser in 5 seconds...
timeout /t 5 /nobreak >nul
start "" "http://localhost:3000"

echo.
echo Both servers are starting...
echo Backend: http://localhost:8080
echo Frontend: http://localhost:3000
echo.
echo Press any key to stop all servers...

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start
    echo.
    echo Try these solutions:
    echo   1. pip install -r requirements.txt
    echo   2. python test_simple_sqlite.py
    echo   3. Check docs folder
    echo.
    pause
    exit /b 1
)

echo.
echo Application started successfully
pause
