{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-26T12:36:16.600Z", "updatedAt": "2025-07-26T12:36:16.607Z", "resourceCount": 6}, "resources": [{"id": "agile-management", "source": "project", "protocol": "execution", "name": "Agile Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/agile-management.execution.md", "metadata": {"createdAt": "2025-07-26T12:36:16.603Z", "updatedAt": "2025-07-26T12:36:16.603Z", "scannedAt": "2025-07-26T12:36:16.603Z", "path": "role/product-manager/execution/agile-management.execution.md"}}, {"id": "product-workflow", "source": "project", "protocol": "execution", "name": "Product Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/product-workflow.execution.md", "metadata": {"createdAt": "2025-07-26T12:36:16.604Z", "updatedAt": "2025-07-26T12:36:16.604Z", "scannedAt": "2025-07-26T12:36:16.604Z", "path": "role/product-manager/execution/product-workflow.execution.md"}}, {"id": "requirement-analysis", "source": "project", "protocol": "execution", "name": "Requirement Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/requirement-analysis.execution.md", "metadata": {"createdAt": "2025-07-26T12:36:16.605Z", "updatedAt": "2025-07-26T12:36:16.605Z", "scannedAt": "2025-07-26T12:36:16.605Z", "path": "role/product-manager/execution/requirement-analysis.execution.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-07-26T12:36:16.605Z", "updatedAt": "2025-07-26T12:36:16.605Z", "scannedAt": "2025-07-26T12:36:16.605Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "product-thinking", "source": "project", "protocol": "thought", "name": "Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/product-manager/thought/product-thinking.thought.md", "metadata": {"createdAt": "2025-07-26T12:36:16.606Z", "updatedAt": "2025-07-26T12:36:16.606Z", "scannedAt": "2025-07-26T12:36:16.606Z", "path": "role/product-manager/thought/product-thinking.thought.md"}}, {"id": "user-empathy", "source": "project", "protocol": "thought", "name": "User Empathy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/product-manager/thought/user-empathy.thought.md", "metadata": {"createdAt": "2025-07-26T12:36:16.607Z", "updatedAt": "2025-07-26T12:36:16.607Z", "scannedAt": "2025-07-26T12:36:16.607Z", "path": "role/product-manager/thought/user-empathy.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"execution": 3, "role": 1, "thought": 2}, "bySource": {"project": 6}}}