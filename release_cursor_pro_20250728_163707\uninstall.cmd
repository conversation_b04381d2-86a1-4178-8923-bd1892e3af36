@echo off
title Cursor Pro - Uninstaller
color 0C

echo ========================================
echo  Cursor Pro - Uninstaller
echo ========================================
echo.

echo This will remove Cursor Pro from your system.
echo.

set /p confirm="Are you sure you want to uninstall? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo Uninstallation cancelled.
    pause
    exit /b
)

echo.
echo Removing Cursor Pro...
echo.

REM 删除安装目录
if exist "C:\Program Files\Cursor Pro" (
    rmdir /s /q "C:\Program Files\Cursor Pro"
    echo ✓ Application files removed
)

REM 删除桌面快捷方式
if exist "%USERPROFILE%\Desktop\Cursor Pro.lnk" (
    del "%USERPROFILE%\Desktop\Cursor Pro.lnk"
    echo ✓ Desktop shortcut removed
)

echo.
echo ========================================
echo  Uninstallation Complete!
echo ========================================
echo.
echo Cursor Pro has been removed from your system.
echo.
pause
