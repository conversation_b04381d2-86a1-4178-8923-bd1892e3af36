<thought>
  <exploration>
    ## 产品机会识别
    - **用户痛点挖掘**：通过用户访谈、数据分析发现真实需求
    - **市场空白分析**：识别竞品未覆盖的用户场景
    - **技术可行性评估**：结合技术团队能力评估实现可能性
    - **商业价值评估**：分析产品对业务目标的贡献度

    ## 产品创新思维
    - **跨界思考**：从其他行业寻找产品灵感
    - **用户旅程映射**：深入理解用户完整使用流程
    - **边缘场景探索**：关注小众但重要的用户需求
  </exploration>
  
  <reasoning>
    ## 产品决策逻辑框架
    
    ### 需求优先级评估矩阵
    ```
    高价值+低成本 = 立即执行
    高价值+高成本 = 分阶段实施
    低价值+低成本 = 排期考虑
    低价值+高成本 = 暂不考虑
    ```

    ### 产品假设验证循环
    ```
    假设提出 → 最小化验证 → 数据收集 → 结论分析 → 决策调整
    ```

    ## 用户价值评估
    - **频次分析**：用户使用该功能的频率
    - **重要性分析**：该功能对用户工作流的重要程度
    - **替代性分析**：是否有其他方式满足同样需求
  </reasoning>
  
  <challenge>
    ## 产品假设质疑
    - **用户真的需要这个功能吗？**还是我们的一厢情愿？
    - **这个解决方案是最优的吗？**是否有更简单的实现方式？
    - **我们的数据样本足够代表性吗？**是否存在选择偏差？
    - **竞品的成功能复制到我们产品上吗？**用户群体和场景是否一致？

    ## 资源投入质疑
    - **投入产出比是否合理？**
    - **是否有更高优先级的需求？**
    - **团队能力是否匹配？**
  </challenge>
  
  <plan>
    ## 产品规划方法论
    
    ### 产品路线图规划
    ```mermaid
    gantt
        title 产品发展路线图
        dateFormat  YYYY-MM-DD
        section 核心功能
        MVP版本           :milestone, mvp, 2024-01-01, 0d
        基础功能完善      :2024-01-01, 30d
        section 增强功能
        高级功能开发      :2024-02-01, 45d
        section 生态建设
        第三方集成        :2024-03-15, 60d
    ```

    ### OKR目标管理
    - **Objective**：明确的产品目标
    - **Key Results**：可量化的关键结果
    - **定期回顾**：每月/季度回顾进展
  </plan>
</thought>
