import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import { pinia } from './stores'
import { useLogStore } from '@/stores/logs'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

const app = createApp(App)

// 配置Pinia状态管理
app.use(pinia)

// 配置Element Plus
app.use(ElementPlus)

// 挂载应用
app.mount('#app')

// 初始化日志存储
const logStore = useLogStore()
logStore.init()

console.log('🚀 Cursor Pro Vue版本已启动')
