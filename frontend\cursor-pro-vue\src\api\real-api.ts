/**
 * 真实API调用 - 无模拟数据版本
 * 只调用真实的后端API，失败就抛出错误
 */

// API基础配置
const API_BASE_URL = 'http://localhost:8080'

// 通用API调用函数
async function callAPI(endpoint: string, data?: any, method: 'GET' | 'POST' = 'POST'): Promise<any> {
  try {
    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    }

    // 如果有数据，添加到请求体
    if (data && method === 'POST') {
      requestOptions.body = JSON.stringify(data)
    }

    const response = await fetch(`${API_BASE_URL}/api/${endpoint}`, requestOptions)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const responseData = await response.json()

    if (responseData.success === false) {
      throw new Error(responseData.error || 'API调用失败')
    }

    // 如果返回的data是字符串，直接返回；否则返回整个responseData
    if (responseData.data !== undefined) {
      return responseData.data
    }

    return responseData
  } catch (error) {
    console.error(`[API] ${endpoint} 调用失败:`, error)
    throw error
  }
}

// 真实API类 - 无模拟数据
export class RealAPI {
  private lastError: string | null = null
  private errorCount = 0

  // 获取账户信息
  async getAccountInfo(): Promise<any> {
    try {
      const result = await callAPI('get_account_info')
      this.errorCount = 0 // 重置错误计数
      this.lastError = null
      return result
    } catch (error) {
      this.errorCount++
      const errorMsg = error instanceof Error ? error.message : String(error)

      // 如果是相同错误且已经重复多次，返回特殊状态
      if (errorMsg === this.lastError && this.errorCount > 3) {
        return {
          email: "Cursor未登录",
          plan: "请先登录Cursor",
          trial_days: 0,
          pro_used: 0,
          pro_total: 0,
          pro_percent: 0,
          basic_total: "需要登录"
        }
      }

      this.lastError = errorMsg
      throw error
    }
  }

  // 获取注册账户列表
  async getRegisteredAccounts(): Promise<any> {
    return await callAPI('get_registered_accounts')
  }

  // 自动注册
  async autoRegister(): Promise<any> {
    const result = await callAPI('auto_register')

    // 如果返回的是对象，检查success字段
    if (typeof result === 'object' && result !== null) {
      if (result.success === false) {
        throw new Error(result.error || '注册失败')
      }
      return result
    }

    // 如果返回的是字符串，直接返回
    return result
  }

  // 获取随机配置
  async getRandomConfig(): Promise<any> {
    const result = await callAPI('get_random_config')

    if (typeof result === 'object' && result !== null) {
      if (result.success === false) {
        throw new Error(result.error || '获取随机配置失败')
      }
      return result
    }

    return result
  }

  // 获取验证码
  async getVerificationCode(): Promise<any> {
    const result = await callAPI('get_verification_code')

    if (typeof result === 'object' && result !== null) {
      if (result.success === false) {
        throw new Error(result.error || '获取验证码失败')
      }
      return result
    }

    return result
  }

  // 初始化Cursor
  async initCursor(): Promise<string> {
    return await callAPI('init_cursor')
  }

  // 重置机器ID
  async resetMachineId(): Promise<string> {
    return await callAPI('reset_machine_id')
  }

  // 获取机器ID
  async getMachineId(): Promise<string> {
    return await callAPI('get_machine_id')
  }

  // 生成机器ID
  async generateMachineId(): Promise<string> {
    return await callAPI('generate_machine_id')
  }

  // 恢复机器ID
  async restoreMachineId(): Promise<string> {
    return await callAPI('restore_machine_id')
  }

  // 备份设置
  async backupSettings(backupName?: string): Promise<string> {
    return await callAPI('backup_settings')
  }

  // 备份会话
  async backupSession(backupName?: string): Promise<string> {
    return await callAPI('backup_session')
  }

  // 列出设置备份
  async listSettingsBackups(): Promise<any> {
    return await callAPI('list_settings_backups')
  }

  // 列出会话备份
  async listSessionBackups(): Promise<any> {
    return await callAPI('list_session_backups')
  }

  // 删除设置备份
  async deleteSettingsBackup(backupName?: string): Promise<string> {
    return await callAPI('delete_settings_backup', { backup_name: backupName })
  }

  // 删除会话备份
  async deleteSessionBackup(backupName?: string): Promise<string> {
    return await callAPI('delete_session_backup', { backup_name: backupName })
  }

  // 恢复设置
  async restoreSettings(backupName?: string): Promise<string> {
    return await callAPI('restore_settings')
  }

  // 恢复会话
  async restoreSession(backupName?: string): Promise<string> {
    return await callAPI('restore_session')
  }

  // 删除备份
  async deleteBackup(backupName?: string): Promise<string> {
    return await callAPI('delete_backup', { backup_name: backupName })
  }

  // 删除账户
  async deleteAccount(email: string): Promise<string> {
    return await callAPI('delete_account', { email })
  }

  // 获取配置文件列表
  async getConfigFiles(): Promise<any> {
    return await callAPI('get_config_files')
  }

  // 获取账户列表
  async getAccountList(): Promise<any[]> {
    return await callAPI('get_account_list')
  }

  // 获取Cursor版本
  async getCursorVersion(): Promise<any> {
    return await callAPI('get_cursor_version')
  }



  // 版本绕过
  async bypassVersionCheck(): Promise<string> {
    return await callAPI('bypass_version_check')
  }



  // 保存账户信息
  async saveAccountInfo(email?: string, password?: string): Promise<string> {
    return await callAPI('save_account')
  }

  // 保存账户（一键流程用）
  async saveAccount(): Promise<string> {
    return await callAPI('save_account')
  }

  // 重启Cursor
  async restartCursor(): Promise<string> {
    return await callAPI('restart_cursor')
  }

  // 手动认证
  async manualAuth(token: string, email?: string, authType?: string): Promise<boolean> {
    return await callAPI('manual_auth')
  }



  // Google注册
  async googleRegister(): Promise<boolean> {
    return await callAPI('google_register')
  }



  // 获取日志
  async getLog(): Promise<string> {
    return await callAPI('get_log')
  }

  // 获取配置信息
  async getConfigInfo(): Promise<string> {
    return await callAPI('get_config_info')
  }

  // 显示配置
  async showConfig(): Promise<string> {
    return await callAPI('show_config')
  }

  // 重置配置
  async resetConfig(): Promise<string> {
    return await callAPI('reset_config')
  }

  // 加载设置
  async loadSettings(): Promise<any> {
    return await callAPI('load_settings')
  }

  // 保存设置
  async saveSettings(settings: any): Promise<boolean> {
    return await callAPI('save_settings', settings)
  }

  // OAuth认证
  async startGoogleOAuth(): Promise<any> {
    return await callAPI('start_google_oauth')
  }



  // 完全重置
  async totallyResetCursor(): Promise<string> {
    return await callAPI('totally_reset_cursor')
  }





  // 自动更新
  async toggleAutoUpdate(): Promise<string> {
    return await callAPI('toggle_auto_update')
  }



  // 打开URL
  async openUrl(url: string): Promise<void> {
    await callAPI('open_url')
  }

  // 显示消息
  async showMessage(message: string): Promise<void> {
    await callAPI('show_message')
  }



  // 获取注册配置
  async getRegisterConfig(): Promise<any> {
    return await callAPI('get_register_config')
  }

  // 保存注册配置
  async saveRegisterConfig(config: any): Promise<boolean> {
    return await callAPI('save_register_config', config)
  }

  // 切换账户
  async switchAccount(email: string, password?: string): Promise<string> {
    return await callAPI('switch_account', { email, password })
  }

  // 恢复账户
  async restoreAccount(backupFile: string): Promise<any> {
    return await callAPI('restore_account', { backup_file: backupFile })
  }

  // 从备份中恢复指定账户
  async restoreAccountFromBackup(email: string): Promise<any> {
    return await callAPI('restore_account_from_backup', { email })
  }

  // 获取备份账户列表
  async listBackupAccounts(): Promise<any> {
    return await callAPI('list_backup_accounts', undefined, 'GET')
  }

  // 获取备份列表
  async listBackups(): Promise<any> {
    return await callAPI('list_backups', undefined, 'GET')
  }

  // 删除账户备份文件
  async deleteAccountBackup(backupFile: string): Promise<any> {
    return await callAPI('delete_account_backup', { backup_file: backupFile })
  }

  // 获取系统信息
  async getSystemInfo(): Promise<any> {
    return await callAPI('get_system_info', undefined, 'GET')
  }

  // 检测Cursor路径
  async detectCursorPaths(): Promise<any> {
    return await callAPI('detect_cursor_paths', undefined, 'GET')
  }

  // 扫描配置文件
  async scanConfigFiles(): Promise<any> {
    return await callAPI('scan_config_files', undefined, 'GET')
  }

  // 获取存储使用情况
  async getStorageUsage(): Promise<any> {
    return await callAPI('get_storage_usage', undefined, 'GET')
  }

  // 检测账户文件
  async detectAccountFiles(): Promise<any> {
    return await callAPI('scan_config_files', undefined, 'GET')
  }
}

// 导出单例实例
export const realAPI = new RealAPI()

// 默认导出
export default realAPI
