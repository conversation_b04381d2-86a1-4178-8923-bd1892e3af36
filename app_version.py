#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用版本配置
打包时的固定版本号，不可修改
"""

# 应用版本信息
APP_VERSION = "1.1.0"
APP_NAME = "Cursor Pro"
BUILD_DATE = "2025-07-28"
BUILD_NUMBER = "152840"

# 版本历史
VERSION_HISTORY = {
    "1.1.0": {
        "date": "2025-07-28",
        "description": "修复版本检查问题，优化用户体验",
        "features": [
            "修复版本号读取问题",
            "优化前后端分离架构", 
            "增强反检测功能",
            "改进用户界面"
        ]
    },
    "1.0.0": {
        "date": "2025-01-20", 
        "description": "初始版本发布",
        "features": [
            "基础功能实现",
            "Vue界面设计",
            "Python后端服务"
        ]
    }
}

def get_app_version():
    """获取应用版本号"""
    return APP_VERSION

def get_app_info():
    """获取完整应用信息"""
    return {
        "version": APP_VERSION,
        "name": APP_NAME,
        "build_date": BUILD_DATE,
        "build_number": BUILD_NUMBER,
        "full_version": f"{APP_VERSION}.{BUILD_NUMBER}"
    }

def get_version_info():
    """获取版本详细信息"""
    return VERSION_HISTORY.get(APP_VERSION, {})

if __name__ == "__main__":
    print(f"应用版本: {APP_VERSION}")
    print(f"构建日期: {BUILD_DATE}")
    print(f"构建编号: {BUILD_NUMBER}")
