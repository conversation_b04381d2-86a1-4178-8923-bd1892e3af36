"""
数据路径管理器
统一管理用户数据存储路径，确保数据存储在合适的位置
"""

import os
import sys
import platform
from pathlib import Path
from typing import Dict, Optional
import shutil
import json
from datetime import datetime


class DataPathManager:
    """数据路径管理器"""
    
    def __init__(self, app_name: str = "cursor-pro"):
        self.app_name = app_name
        self.system = platform.system()
        self._base_data_dir = None
        self._paths = {}
        
        # 初始化路径
        self._init_paths()
    
    def _init_paths(self):
        """初始化所有路径"""
        # 获取基础数据目录
        self._base_data_dir = self._get_base_data_dir()
        
        # 定义子目录结构
        self._paths = {
            'base': self._base_data_dir,
            'data': self._base_data_dir / 'data',
            'config': self._base_data_dir / 'config', 
            'logs': self._base_data_dir / 'logs',
            'backups': self._base_data_dir / 'backups',
            'temp': self._base_data_dir / 'temp',
            'cache': self._base_data_dir / 'cache'
        }
        
        # 创建所有目录
        self._ensure_directories()
    
    def _get_base_data_dir(self) -> Path:
        """获取基础数据目录"""
        if self.system == "Windows":
            # Windows: %APPDATA%\cursor-pro
            appdata = os.getenv('APPDATA')
            if not appdata:
                raise EnvironmentError("APPDATA environment variable not found")
            return Path(appdata) / self.app_name
            
        elif self.system == "Darwin":  # macOS
            # macOS: ~/Library/Application Support/cursor-pro
            return Path.home() / "Library" / "Application Support" / self.app_name
            
        elif self.system == "Linux":
            # Linux: ~/.config/cursor-pro
            return Path.home() / ".config" / self.app_name
            
        else:
            # 其他系统，使用用户目录下的隐藏文件夹
            return Path.home() / f".{self.app_name}"
    
    def _ensure_directories(self):
        """确保所有目录存在"""
        for name, path in self._paths.items():
            try:
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ 目录已创建: {path}")
            except Exception as e:
                print(f"❌ 创建目录失败 {path}: {e}")
                raise
    
    def get_path(self, path_type: str) -> Path:
        """获取指定类型的路径"""
        if path_type not in self._paths:
            raise ValueError(f"未知的路径类型: {path_type}")
        return self._paths[path_type]
    
    def get_file_path(self, path_type: str, filename: str) -> Path:
        """获取指定类型目录下的文件路径"""
        return self.get_path(path_type) / filename
    
    # 便捷方法
    def get_data_file(self, filename: str) -> Path:
        """获取数据文件路径"""
        return self.get_file_path('data', filename)
    
    def get_config_file(self, filename: str) -> Path:
        """获取配置文件路径"""
        return self.get_file_path('config', filename)
    
    def get_log_file(self, filename: str) -> Path:
        """获取日志文件路径"""
        return self.get_file_path('logs', filename)
    
    def get_backup_file(self, filename: str) -> Path:
        """获取备份文件路径"""
        return self.get_file_path('backups', filename)
    
    def get_temp_file(self, filename: str) -> Path:
        """获取临时文件路径"""
        return self.get_file_path('temp', filename)
    
    def get_cache_file(self, filename: str) -> Path:
        """获取缓存文件路径"""
        return self.get_file_path('cache', filename)
    
    def list_files(self, path_type: str, pattern: str = "*") -> list:
        """列出指定目录下的文件"""
        directory = self.get_path(path_type)
        return list(directory.glob(pattern))
    
    def get_directory_size(self, path_type: str) -> int:
        """获取目录大小（字节）"""
        directory = self.get_path(path_type)
        total_size = 0
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                try:
                    total_size += file_path.stat().st_size
                except (OSError, IOError):
                    continue
        
        return total_size
    
    def clean_temp_files(self):
        """清理临时文件"""
        temp_dir = self.get_path('temp')
        try:
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
            print(f"✅ 临时文件已清理: {temp_dir}")
        except Exception as e:
            print(f"❌ 清理临时文件失败: {e}")
    
    def get_info(self) -> Dict:
        """获取路径管理器信息"""
        info = {
            'app_name': self.app_name,
            'system': self.system,
            'base_directory': str(self._base_data_dir),
            'paths': {name: str(path) for name, path in self._paths.items()},
            'directory_sizes': {}
        }
        
        # 计算各目录大小
        for name in self._paths.keys():
            if name != 'base':
                try:
                    size = self.get_directory_size(name)
                    info['directory_sizes'][name] = {
                        'bytes': size,
                        'formatted': self._format_size(size)
                    }
                except Exception:
                    info['directory_sizes'][name] = {'bytes': 0, 'formatted': '0 B'}
        
        return info
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def export_info(self, output_file: Optional[str] = None) -> str:
        """导出路径信息到JSON文件"""
        info = self.get_info()
        info['export_time'] = datetime.now().isoformat()
        
        if not output_file:
            output_file = self.get_config_file('path_info.json')
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        
        return str(output_file)


# 全局实例
data_path_manager = DataPathManager()


def get_data_path_manager() -> DataPathManager:
    """获取数据路径管理器实例"""
    return data_path_manager


if __name__ == "__main__":
    # 测试代码
    manager = DataPathManager()
    
    print("=== 数据路径管理器信息 ===")
    info = manager.get_info()
    
    print(f"应用名称: {info['app_name']}")
    print(f"操作系统: {info['system']}")
    print(f"基础目录: {info['base_directory']}")
    
    print("\n=== 目录路径 ===")
    for name, path in info['paths'].items():
        print(f"{name:10}: {path}")
    
    print("\n=== 目录大小 ===")
    for name, size_info in info['directory_sizes'].items():
        print(f"{name:10}: {size_info['formatted']}")
    
    # 导出信息
    export_file = manager.export_info()
    print(f"\n✅ 路径信息已导出到: {export_file}")
