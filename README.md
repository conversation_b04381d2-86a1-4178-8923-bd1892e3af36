# 🚀 Cursor Pro - 使用指南

> ## ⚠️ **重要声明 - 仅供学习研究使用**
>
> 🎓 **本项目仅供学习和研究目的使用**
>
> - 📚 **学习目的**: 用于学习编程技术、软件架构和开发实践
> - 🔬 **研究目的**: 用于研究软件逆向工程、系统集成等技术领域
> - ❌ **禁止商业使用**: 严禁将本软件用于任何商业目的或盈利活动
> - ⚖️ **风险自担**: 使用者需自行承担所有使用风险和法律后果
>
> **🚨 风险提示**: 使用本软件可能违反Cursor官方服务条款，可能导致账户被封禁。建议支持正版软件，购买官方授权。
>
> **📋 详细免责声明请查看**: [DISCLAIMER.md](DISCLAIMER.md)

---

## 📋 概述

Cursor Pro 是一个专业的Cursor账户管理工具，现在采用**标准Python包结构**和**纯SQLite数据库**，提供轻量级、零配置的数据管理解决方案。

### 🏗️ 项目结构

```
cursor-pro/
├── src/cursor_pro/          # 主包
│   ├── core/               # 核心功能
│   ├── auth/               # 认证模块
│   ├── backup/             # 备份模块
│   ├── anti_detection/     # 反检测模块
│   ├── management/         # 管理模块
│   ├── utils/              # 工具模块
│   └── database/           # 数据库模块
├── frontend/               # 前端项目
├── scripts/                # 构建脚本
├── tests/                  # 测试目录
├── docs/                   # 文档目录
└── requirements.txt        # 依赖文件
```

## 🎯 SQLite版本优势

### ✅ **纯SQLite的优势**
- 🚀 **零配置**: 无需安装数据库服务器，开箱即用
- 📦 **轻量级**: 文件型数据库，便于备份和迁移
- ⚡ **高性能**: 启动速度快，内存占用低
- 🔧 **易维护**: 无需复杂的数据库管理
- 🌐 **跨平台**: 完美支持Windows、macOS、Linux

### 🆚 **性能对比**

| 指标 | 之前MySQL版本 | 现在SQLite版本 | 提升 |
|------|---------------|----------------|------|
| 启动时间 | 10-15秒 | 1-2秒 | **10倍** |
| 内存占用 | 200MB+ | 10-20MB | **90%减少** |
| 安装复杂度 | 需要MySQL服务器 | 零配置 | **极简** |
| 备份方式 | mysqldump | 复制文件 | **简单** |
| 部署难度 | 中等 | 极简 | **零门槛** |

## 🛠️ 安装和使用

### 🚀 方法1: 一键安装启动（推荐新手）

```bash
# 双击运行一键安装脚本
install_and_run.bat
```

**自动完成所有步骤**：
- ✅ 检查系统环境（Python + Node.js）
- ✅ 创建虚拟环境和安装依赖
- ✅ 构建前端项目
- ✅ 初始化SQLite数据库
- ✅ 启动应用并打开浏览器

### ⚡ 方法2: 快速启动（已安装用户）

```bash
# 双击运行快速启动脚本
quick_start.bat
```

### 🔧 方法3: 传统方式（高级用户）

#### 3.1 安装Python依赖

```bash
# 使用新的依赖文件
pip install -r requirements_new.txt

# 或使用传统方式
pip install -r requirements.txt
```

#### 3.2 启动应用

```bash
# 启动SQLite版本
python start_sqlite.py

# 或直接启动主应用
python vue_with_api_optimized.py
```

#### 3.3 访问应用

```bash
# 应用启动后自动打开浏览器，或手动访问
http://localhost:8000
```

### 📚 详细安装指南

查看完整安装文档：[INSTALL_GUIDE.md](INSTALL_GUIDE.md)

## 🔧 配置管理

### 数据库配置

SQLite数据库自动配置，无需手动设置：

```bash
# 数据库文件位置
# Windows: %USERPROFILE%\Documents\.cursor-pro\cursor_pro.db
# macOS/Linux: ~/.cursor-pro/cursor_pro.db
```

### 查看数据库状态

```bash
# 查看SQLite数据库状态
python database_status.py status

# 或直接切换
python -c "from database_switch import DatabaseSwitch; DatabaseSwitch().switch_to_mysql()"
```

## 🚀 启动方式

### 方式1: 一键启动
```bash
start_mysql.bat  # Windows
# 或
./start_mysql.sh  # Linux/macOS
```

### 方式2: 分步启动
```bash
# 1. 启动后端
python vue_with_api_optimized.py

# 2. 启动前端
cd cursor-pro-vue
npm run dev
```

## 🧪 测试和验证

### 运行完整测试
```bash
python test_mysql.py
```

### 单项测试
```bash
# 测试数据库连接
python test_mysql.py connection

# 测试认证功能
python test_mysql.py auth

# 测试数据库切换
python test_mysql.py switch

# 测试API集成
python test_mysql.py api
```

## 📊 数据迁移

### 从SQLite迁移到MySQL

```bash
# 运行迁移工具
python migrate_to_mysql.py
```

迁移工具会：
1. ✅ 检查MySQL连接
2. ✅ 创建数据库表结构
3. ✅ 备份原SQLite数据
4. ✅ 迁移认证信息
5. ✅ 迁移配置数据
6. ✅ 验证迁移结果

### 迁移后验证

```bash
# 检查数据完整性
python -c "
from cursor_auth_mysql import CursorAuthMySQL
auth = CursorAuthMySQL()
print('认证数据:', len(auth.get_auth_info() or {}))
print('账户数据:', len(auth.get_cursor_accounts()))
"
```

## 🔍 故障排除

### 常见问题

#### 1. MySQL连接失败
```bash
# 检查MySQL服务状态
# Windows
net start mysql

# Linux/macOS
sudo systemctl status mysql
# 或
brew services list | grep mysql
```

#### 2. 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 重新安装依赖
pip install --force-reinstall mysql-connector-python PyMySQL SQLAlchemy
```

#### 3. 数据库初始化失败
```bash
# 手动创建数据库
mysql -u root -p -e "CREATE DATABASE cursor_pro CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 重新初始化
python -c "from database.config import init_database; init_database()"
```

#### 4. 权限问题
```bash
# 创建专用用户
mysql -u root -p -e "
CREATE USER 'cursor_user'@'localhost' IDENTIFIED BY 'cursor_pass';
GRANT ALL PRIVILEGES ON cursor_pro.* TO 'cursor_user'@'localhost';
FLUSH PRIVILEGES;
"

```

## 📈 性能优化

### SQLite优化配置

项目已自动应用以下SQLite优化：

```sql
PRAGMA foreign_keys = ON;      -- 启用外键约束
PRAGMA journal_mode = WAL;     -- 使用WAL模式提高并发
PRAGMA synchronous = NORMAL;   -- 平衡性能和安全性
```

### 应用层优化

```python
# SQLite连接配置
SQLITE_CONFIG = {
    'timeout': 30.0,
    'check_same_thread': False,
    'isolation_level': None  # 自动提交模式
}
```

## 🔄 版本切换

### 切换回SQLite
```bash
python -c "from database_switch import DatabaseSwitch; DatabaseSwitch().switch_to_sqlite()"
```

### 查看当前版本
```bash
python -c "from database_switch import DatabaseSwitch; DatabaseSwitch().show_status()"
```

## 📞 技术支持

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看MySQL错误日志
# Linux: /var/log/mysql/error.log
# Windows: MySQL安装目录/data/*.err
```

### 问题反馈

如遇到问题，请提供：
1. 错误信息截图
2. 系统环境信息
3. MySQL版本信息
4. 操作步骤描述

## 🎉 总结

MySQL版本为Cursor Pro带来了：
- 🌐 企业级数据库支持
- 👥 多用户协作能力
- 🔄 云端数据同步
- 📊 更强的性能表现

立即体验MySQL版本的强大功能！
