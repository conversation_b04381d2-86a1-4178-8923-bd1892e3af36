/* 原版Cursor Pro完整样式 - 一比一还原 */

.app-container {
  display: flex;
  height: 100vh;
  box-shadow: none;
  border-radius: 18px;
  overflow: hidden;
  background: #181c20;
}

/* 侧边栏 */
.sidebar {
  width: 250px;
  background: #23272e;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0 10px 0;
  box-shadow: none;
  min-width: 200px;
  border-right: 1.5px solid #22262c;
}

.logo {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: #fff;
  object-fit: contain;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(96,165,250,0.10);
  border: 2.5px solid #60a5fa;
}

.sidebar-title {
  font-size: 1.6rem;
  font-weight: 800;
  color: #60a5fa;
  margin-top: 24px;
  margin-bottom: 36px;
  letter-spacing: 2.5px;
  text-shadow: 0 2px 8px #23272e44;
}

.menu {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 14px;
  align-items: center;
}

.menu-btn {
  width: 82%;
  background: none;
  border: none;
  color: #e5e5e5;
  font-size: 1.12rem;
  padding: 12px 0 12px 24px;
  text-align: left;
  border-radius: 10px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, border 0.18s, box-shadow 0.18s;
  margin-bottom: 2px;
  letter-spacing: 1px;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.menu-btn.active, .menu-btn:hover {
  background: #34bfa3;
  color: #fff;
  font-weight: bold;
  border-left: none;
  box-shadow: 0 2px 12px rgba(16,185,129,0.10);
  border-radius: 16px;
  transition: background 0.18s, color 0.18s, border-radius 0.18s;
}

/* 选中状态添加白色边框 */
.menu-btn.active {
  border: 2px solid #ffffff;
  box-shadow: 0 2px 12px rgba(16,185,129,0.10), 0 0 0 1px rgba(255,255,255,0.8);
}

.sidebar-bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  padding-bottom: 16px;
}

.exit-btn {
  width: 82%;
  background: #23272e;
  border: 2px solid #f87171;
  color: #f87171;
  font-size: 1.12rem;
  padding: 12px 0;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.18s, color 0.18s, border 0.18s, box-shadow 0.18s;
  font-weight: bold;
  box-shadow: none;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.exit-btn:hover {
  background: #f87171;
  color: #181c20;
  border: 2px solid #f87171;
  box-shadow: 0 4px 16px rgba(248,113,113,0.18);
}

/* 主内容区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 0 3px 0;
  height: 100vh;
  overflow: hidden;
  background: #181c20;
  box-shadow: none;
}

.top-bar {
  background: #23272e;
  padding: 22px 38px 12px 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1.5px solid #2d323a;
  box-shadow: 0 2px 12px rgba(96,165,250,0.04);
}

.version-info {
  color: #facc15;
  font-size: 1.12rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px #23272e44;
}



.content-area {
  flex: 1;
  padding: 28px 38px 18px 38px;
  overflow-y: auto;
  background: #181c20;
}

/* 页面标题 */
.page-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #60a5fa;
  margin-bottom: 28px;
  letter-spacing: 2px;
  text-shadow: 0 2px 12px rgba(96,165,250,0.15);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 卡片样式 */
.card {
  background: #23272e;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1.5px solid #2d323a;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.card-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #60a5fa;
  margin-bottom: 16px;
  letter-spacing: 1px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.card-content {
  color: #e5e5e5;
  line-height: 1.6;
}

/* 按钮样式 */
.btn {
  background: #34bfa3;
  color: #fff;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.18s;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  letter-spacing: 0.5px;
}

.btn:hover {
  background: #2dd4aa;
  box-shadow: 0 4px 16px rgba(52,191,163,0.25);
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6366f1;
}

.btn-secondary:hover {
  background: #7c3aed;
  box-shadow: 0 4px 16px rgba(99,102,241,0.25);
}

.btn-danger {
  background: #ef4444;
}

.btn-danger:hover {
  background: #dc2626;
  box-shadow: 0 4px 16px rgba(239,68,68,0.25);
}

/* 输入框样式 */
.input {
  background: #2d323a;
  border: 1.5px solid #3d424a;
  color: #e5e5e5;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  width: 100%;
  transition: border-color 0.18s;
}

.input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96,165,250,0.1);
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: #23272e;
  border-radius: 12px;
  overflow: hidden;
  border: 1.5px solid #2d323a;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #2d323a;
  color: #e5e5e5;
}

.table th {
  background: #2d323a;
  font-weight: 600;
  color: #60a5fa;
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:hover {
  background: rgba(96,165,250,0.05);
}

/* 滚动条样式 */
.main-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 400px;
}

.main-scroll::-webkit-scrollbar {
  width: 6px;
}

.main-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.main-scroll::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.main-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 165, 250, 0.5);
}

/* log-area 内的滚动条样式 */
.log-area::-webkit-scrollbar {
  width: 4px;
}

.log-area::-webkit-scrollbar-track {
  background: transparent;
}

.log-area::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 2px;
}

.log-area::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

.content-section {
  transition: opacity 0.2s;
  border-radius: 12px;
  box-shadow: none;
  background: #181a20;
  padding: 12px 28px 10px 28px;
  margin-bottom: 0;
}

.content-section:last-child {
  margin-bottom: 0;
}

/* 账号信息卡片 - 增强版紫色风格 */
.account-card {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  border: none;
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
  overflow: hidden;
}

.account-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(139, 92, 246, 0.4);
}

/* 顶部装饰条 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 50%, #FFD700 100%);
  border-radius: 24px 24px 0 0;
}

/* 装饰点 */
.dots-decoration {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  animation: dotPulse 2s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes dotPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

.card-header-new {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 20px;
  position: relative;
}

.email-new {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.email-new::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #FFD700;
  border-radius: 50%;
  flex-shrink: 0;
  animation: emailDot 3s ease-in-out infinite;
}

@keyframes emailDot {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.3); }
}

.plan-badge-new {
  background: #fbbf24;
  color: #1f2937;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
}
