# Cursor Pro - SQLite数据库版本

> 📁 此文档已移动到 `docs/README_SQLITE.md`，请查看最新版本。

## 🎯 项目概述

本项目已成功迁移到SQLite数据库，提供更简单、更轻量的数据存储解决方案。

### ✨ SQLite版本的优势

- **🚀 零配置**: 无需安装数据库服务器，开箱即用
- **📦 轻量级**: 文件型数据库，便于备份和迁移  
- **🔧 易维护**: 无需复杂的数据库管理
- **⚡ 高性能**: 足够支持项目的所有需求
- **🌐 跨平台**: 完美支持Windows、macOS、Linux

## 📁 项目结构

```
cursor-pro-main/
├── database/
│   ├── config.py              # 纯SQLite数据库配置
│   └── sqlite_init.sql        # SQLite初始化脚本
├── cursor_auth.py             # 纯SQLite认证管理器
├── database_status.py         # SQLite数据库状态工具
├── start_sqlite.py            # SQLite启动器
├── start_sqlite.bat           # Windows启动脚本
├── test_simple_sqlite.py      # SQLite功能测试
└── requirements.txt           # 依赖文件（纯SQLite，无MySQL依赖）
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

> 注意：SQLite是Python内置模块，无需额外安装

### 2. 启动应用

```bash
# 使用SQLite启动器
python start_sqlite.py

# 或直接启动主应用
python vue_with_api_optimized.py
```

启动器会自动：
- ✅ 检查SQLite数据库
- ✅ 初始化表结构（如需要）
- ✅ 启动Web应用
- ✅ 打开浏览器访问

### 3. 测试数据库功能

```bash
python test_simple_sqlite.py
```

## 🔧 数据库管理

### 查看数据库状态

```bash
python database_switch.py status
```

### 切换数据库类型

```bash
# 切换到SQLite（默认）
python database_switch.py sqlite

# 切换到MySQL（需要先安装MySQL依赖）
python database_switch.py mysql
```

### 数据库文件位置

- **Windows**: `%USERPROFILE%\Documents\.cursor-pro\cursor_pro.db`
- **macOS/Linux**: `~/.cursor-pro/cursor_pro.db`

## 📊 数据库结构

### 主要表结构

1. **users** - 用户信息表
   - id, email, password_hash, device_id
   - created_at, updated_at, last_login, status

2. **cursor_auth** - 认证信息表
   - id, user_id, auth_key, auth_value
   - created_at, updated_at

3. **cursor_accounts** - 账户表
   - id, user_id, email, access_token, refresh_token
   - auth_type, is_active, created_at, updated_at

4. **user_configs** - 用户配置表
5. **backup_records** - 备份记录表
6. **operation_logs** - 操作日志表

## 💻 代码使用示例

### 基本数据库操作

```python
from database.sqlite_config import get_db_connection_context

# 使用上下文管理器操作数据库
with get_db_connection_context() as conn:
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    cursor.close()
```

### 认证管理

```python
from cursor_auth_sqlite import CursorAuthSQLite

# 创建认证管理器
auth = CursorAuthSQLite(user_id=1)

# 更新认证信息
auth.update_auth(
    email="<EMAIL>",
    access_token="your_access_token",
    refresh_token="your_refresh_token"
)

# 获取认证信息
auth_info = auth.get_auth_info()
print(auth_info)

# 获取账户列表
accounts = auth.get_cursor_accounts()
print(accounts)
```

## 🔄 从MySQL迁移

如果你之前使用MySQL版本，可以使用以下方法迁移：

### 自动迁移

```bash
python migrate_mysql_to_sqlite.py
```

### 手动迁移步骤

1. **备份MySQL数据**
   ```bash
   mysqldump -u root -p cursor_pro > backup.sql
   ```

2. **运行迁移脚本**
   ```bash
   python migrate_mysql_to_sqlite.py
   ```

3. **验证迁移结果**
   ```bash
   python test_simple_sqlite.py
   ```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库文件权限问题
```bash
# Windows
icacls "path\to\cursor_pro.db" /grant Users:F

# Linux/macOS
chmod 644 ~/.cursor-pro/cursor_pro.db
```

#### 2. 数据库锁定问题
```python
# 检查是否有其他进程在使用数据库
import sqlite3
conn = sqlite3.connect('cursor_pro.db', timeout=30)
```

#### 3. 重新初始化数据库
```bash
python -c "
from database.sqlite_config import init_database
init_database()
"
```

### 日志查看

```python
import logging
logging.basicConfig(level=logging.INFO)

# 然后运行你的代码，会显示详细日志
```

## 📈 性能优化

### SQLite优化设置

项目已自动应用以下优化：

```sql
PRAGMA foreign_keys = ON;      -- 启用外键约束
PRAGMA journal_mode = WAL;     -- 使用WAL模式提高并发
PRAGMA synchronous = NORMAL;   -- 平衡性能和安全性
```

### 备份建议

```bash
# 定期备份数据库文件
cp ~/.cursor-pro/cursor_pro.db ~/.cursor-pro/backup/cursor_pro_$(date +%Y%m%d).db
```

## 🔙 回滚到MySQL

如果需要回滚到MySQL：

1. **安装MySQL依赖**
   ```bash
   pip install mysql-connector-python PyMySQL SQLAlchemy
   ```

2. **切换到MySQL**
   ```bash
   python database_switch.py mysql
   ```

3. **恢复MySQL配置**
   ```bash
   # 如果有备份配置文件
   cp .mysql_config.backup .mysql_config
   ```

## 📞 技术支持

### 获取帮助

- 查看日志文件了解详细错误信息
- 运行测试脚本诊断问题
- 检查数据库文件权限和路径

### 问题反馈

如遇到问题，请提供：
1. 错误信息截图
2. 系统环境信息
3. 数据库文件路径和大小
4. 操作步骤描述

## 🎉 总结

SQLite版本为Cursor Pro带来了：
- 🌟 **简化部署**: 无需数据库服务器
- 🌟 **降低维护**: 零配置数据库
- 🌟 **提高可靠性**: 文件型存储更稳定
- 🌟 **增强便携性**: 数据库文件可随项目移动

立即体验SQLite版本的便利性！
