import os
import shutil
import json
import datetime
import platform
from colorama import Fore, Style, init
from config import get_config

# 初始化colorama
init()

# 定义emoji常量
EMOJI = {
    'BACKUP': '💾',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'INFO': 'ℹ️',
    'FOLDER': '📁',
    'FILE': '📄',
    'WARNING': '⚠️'
}

def backup_settings(backup_name=None, translator=None):
    """备份Cursor设置"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('backup.starting') if translator else '开始备份设置...'}{Style.RESET_ALL}")
        
        # 获取配置
        config = get_config(translator)
        if not config:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.config_error') if translator else '无法加载配置'}{Style.RESET_ALL}")
            return False
        
        # 确定备份目录 - 使用统一的数据目录
        try:
            from data_path_manager import DataPathManager
            path_manager = DataPathManager()
            backup_dir = str(path_manager.get_path('backups') / "settings_backups")
        except:
            # 备用方案
            backup_dir = os.path.join(os.path.expandvars("%APPDATA%"), "cursor-pro", "backups", "settings_backups")
        os.makedirs(backup_dir, exist_ok=True)
        
        # 如果没有提供备份名称，使用时间戳
        if not backup_name:
            backup_name = f"settings_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建备份子目录
        backup_path = os.path.join(backup_dir, backup_name)
        os.makedirs(backup_path, exist_ok=True)
        
        # 获取Cursor设置文件路径
        system = platform.system()

        # 获取基础路径
        if system == "Windows":
            if not config.has_section('WindowsPaths'):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.config_error') if translator else '无法加载Windows路径配置'}{Style.RESET_ALL}")
                return False
            storage_path = config.get('WindowsPaths', 'storage_path', fallback='')
            sqlite_path = config.get('WindowsPaths', 'sqlite_path', fallback='')
        elif system == "Darwin":  # macOS
            if not config.has_section('MacPaths'):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.config_error') if translator else '无法加载macOS路径配置'}{Style.RESET_ALL}")
                return False
            storage_path = config.get('MacPaths', 'storage_path', fallback='')
            sqlite_path = config.get('MacPaths', 'sqlite_path', fallback='')
        elif system == "Linux":
            if not config.has_section('LinuxPaths'):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.config_error') if translator else '无法加载Linux路径配置'}{Style.RESET_ALL}")
                return False
            storage_path = config.get('LinuxPaths', 'storage_path', fallback='')
            sqlite_path = config.get('LinuxPaths', 'sqlite_path', fallback='')
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.unsupported_system') if translator else '不支持的操作系统'}{Style.RESET_ALL}")
            return False

        # 验证路径存在
        if not storage_path or not os.path.exists(storage_path):
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('backup.storage_not_found') if translator else 'storage.json文件不存在'}{Style.RESET_ALL}")
            return False

        if not sqlite_path or not os.path.exists(sqlite_path):
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('backup.sqlite_not_found') if translator else 'SQLite数据库文件不存在'}{Style.RESET_ALL}")
            return False

        # 备份信息
        backup_info = {"files": [], "settings": {}, "timestamp": str(datetime.datetime.now()), "system": system}

        # 1. 备份storage.json中的用户设置（排除认证和机器ID信息）
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('backup.processing_storage') if translator else '正在处理storage.json用户设置...'}{Style.RESET_ALL}")

            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)

            # 提取用户设置（排除认证和机器相关信息）
            user_settings = {}
            exclude_keys = [
                'telemetry.machineId', 'telemetry.macMachineId', 'telemetry.devDeviceId',
                'telemetry.sqmId', 'storage.serviceMachineId', 'cursorAuth/cachedEmail',
                'cursorAuth/accessToken', 'cursorAuth/refreshToken', 'cursorAuth/cachedSignUpType'
            ]

            for key, value in storage_data.items():
                if key not in exclude_keys:
                    user_settings[key] = value

            # 保存用户设置到备份
            settings_file = os.path.join(backup_path, "user_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(user_settings, f, indent=2, ensure_ascii=False)

            backup_info["files"].append({
                "original_path": storage_path,
                "backup_path": settings_file,
                "filename": "user_settings.json",
                "size": os.path.getsize(settings_file),
                "type": "user_settings"
            })

            print(f"{Fore.GREEN}{EMOJI['FILE']} {translator.get('backup.settings_backed_up') if translator else '已备份用户设置'}{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.settings_error', error=str(e)) if translator else f'备份用户设置时出错: {str(e)}'}{Style.RESET_ALL}")

        # 2. 备份SQLite中的用户设置（排除认证信息）
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('backup.processing_sqlite') if translator else '正在处理SQLite用户设置...'}{Style.RESET_ALL}")

            import sqlite3
            conn = sqlite3.connect(sqlite_path)
            cursor = conn.cursor()

            # 获取所有键值对，排除认证相关
            cursor.execute("SELECT key, value FROM ItemTable")
            all_items = cursor.fetchall()

            user_sqlite_settings = {}
            exclude_auth_keys = [
                'cursorAuth/cachedEmail', 'cursorAuth/accessToken',
                'cursorAuth/refreshToken', 'cursorAuth/cachedSignUpType'
            ]

            for key, value in all_items:
                if not any(auth_key in key for auth_key in exclude_auth_keys):
                    user_sqlite_settings[key] = value

            conn.close()

            # 保存SQLite用户设置
            sqlite_settings_file = os.path.join(backup_path, "sqlite_user_settings.json")
            with open(sqlite_settings_file, 'w', encoding='utf-8') as f:
                json.dump(user_sqlite_settings, f, indent=2, ensure_ascii=False)

            backup_info["files"].append({
                "original_path": sqlite_path,
                "backup_path": sqlite_settings_file,
                "filename": "sqlite_user_settings.json",
                "size": os.path.getsize(sqlite_settings_file),
                "type": "sqlite_settings"
            })

            print(f"{Fore.GREEN}{EMOJI['FILE']} {translator.get('backup.sqlite_settings_backed_up') if translator else '已备份SQLite用户设置'}{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.sqlite_error', error=str(e)) if translator else f'备份SQLite设置时出错: {str(e)}'}{Style.RESET_ALL}")
        
        # 保存备份元数据
        metadata_path = os.path.join(backup_path, "backup_info.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, indent=2)
        
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('backup.success', count=len(backup_info['files']), path=backup_path) if translator else f'成功备份 {len(backup_info['files'])} 个文件到 {backup_path}'}{Style.RESET_ALL}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.error', error=str(e)) if translator else f'备份设置时出错: {str(e)}'}{Style.RESET_ALL}")
        return False

def list_backups(translator=None):
    """列出所有备份"""
    try:
        # 使用数据路径管理器获取正确的备份目录
        try:
            from data_path_manager import get_data_path_manager
            manager = get_data_path_manager()
            backup_dir = os.path.join(manager.get_path('backups'), "settings_backups")
        except ImportError:
            # 备用路径
            backup_dir = os.path.join(os.path.expanduser("~"), "Documents", ".cursor-pro", "backups", "settings_backups")
        if not os.path.exists(backup_dir):
            print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('backup.no_backups') if translator else '没有找到备份'}{Style.RESET_ALL}")
            return []
        
        backups = [d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d))]
        if not backups:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('backup.no_backups') if translator else '没有找到备份'}{Style.RESET_ALL}")
            return []
        
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('backup.found_backups', count=len(backups)) if translator else f'找到 {len(backups)} 个备份:'}{Style.RESET_ALL}")
        
        backup_details = []
        for backup in backups:
            metadata_path = os.path.join(backup_dir, backup, "backup_info.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)
                    backup_details.append({
                        "name": backup,
                        "timestamp": info.get("timestamp", "Unknown"),
                        "files": len(info.get("files", [])),
                        "system": info.get("system", "Unknown")
                    })
                except:
                    backup_details.append({
                        "name": backup,
                        "timestamp": "Error reading metadata",
                        "files": 0,
                        "system": "Unknown"
                    })
        
        # 按时间排序
        backup_details.sort(key=lambda x: x["name"], reverse=True)
        
        for i, backup in enumerate(backup_details, 1):
            print(f"{Fore.CYAN}{i}. {backup['name']} - {backup['timestamp']} ({backup['files']} files, {backup['system']}){Style.RESET_ALL}")
        
        return backup_details
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('backup.list_error', error=str(e)) if translator else f'列出备份时出错: {str(e)}'}{Style.RESET_ALL}")
        return []

def main(translator=None):
    """主函数"""
    return backup_settings(translator=translator)

if __name__ == "__main__":
    main() 