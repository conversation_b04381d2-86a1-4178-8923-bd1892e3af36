<execution>
  <constraint>
    ## 敏捷管理客观限制
    - **团队规模限制**：团队大小影响沟通效率和协作方式
    - **技能差异**：团队成员技能水平和经验的差异
    - **外部依赖**：第三方服务、外部团队的依赖关系
    - **变更成本**：频繁变更可能带来的技术债务和团队疲劳
  </constraint>

  <rule>
    ## 敏捷管理强制规则
    - **迭代时间固定**：Sprint周期一旦确定不可随意更改
    - **需求变更控制**：Sprint进行中原则上不接受新需求
    - **每日同步必须**：团队必须进行每日站会同步
    - **回顾改进强制**：每个Sprint结束必须进行回顾和改进
    - **可工作软件**：每个Sprint必须交付可工作的软件增量
  </rule>

  <guideline>
    ## 敏捷管理指导原则
    - **个体和互动胜过流程和工具**：重视人的因素和团队协作
    - **工作的软件胜过详尽的文档**：以可用的产品为核心目标
    - **客户合作胜过合同谈判**：与用户保持密切沟通和反馈
    - **响应变化胜过遵循计划**：快速适应需求和环境变化
  </guideline>

  <process>
    ## 敏捷开发标准流程
    
    ### Sprint规划流程
    ```mermaid
    flowchart TD
        A[Product Backlog梳理] --> B[Sprint目标确定]
        B --> C[Story估算]
        C --> D[Sprint Backlog确定]
        D --> E[任务分解]
        E --> F[Sprint启动]
        
        A1[需求优先级排序] --> A
        A2[Story准备就绪检查] --> A
    ```

    ### 每日站会流程
    ```mermaid
    flowchart LR
        A[昨天完成了什么] --> B[今天计划做什么]
        B --> C[遇到什么阻碍]
        C --> D[需要什么帮助]
        D --> E[风险和依赖]
        E --> F[行动计划]
    ```

    ### Sprint回顾流程
    ```mermaid
    flowchart TD
        A[Sprint Review] --> B[产品演示]
        B --> C[用户反馈收集]
        C --> D[Sprint Retrospective]
        D --> E[团队反思]
        E --> F[改进行动计划]
        F --> G[下Sprint规划]
    ```

    ### 敏捷度量指标
    ```mermaid
    graph LR
        A[速度指标] --> A1[Story Points完成率]
        A --> A2[燃尽图趋势]
        
        B[质量指标] --> B1[缺陷率]
        B --> B2[技术债务]
        
        C[价值指标] --> C1[用户满意度]
        C --> C2[功能使用率]
        
        D[团队指标] --> D1[团队满意度]
        D --> D2[协作效率]
    ```

    ### 用户故事标准格式
    ```
    作为 [用户角色]
    我希望 [功能描述]
    以便 [价值目标]

    验收标准：
    - 给定 [前置条件]
    - 当 [执行动作]
    - 那么 [预期结果]

    定义完成(DoD)：
    - 代码开发完成
    - 单元测试通过
    - 代码审查完成
    - 功能测试通过
    - 文档更新完成
    ```
  </process>

  <criteria>
    ## 敏捷管理质量标准
    
    ### Sprint交付质量
    - ✅ Sprint目标达成率 ≥ 80%
    - ✅ Story Points完成率 ≥ 85%
    - ✅ 缺陷逃逸率 ≤ 5%
    - ✅ 技术债务控制在合理范围

    ### 团队协作效率
    - ✅ 每日站会参与率 ≥ 95%
    - ✅ Sprint回顾改进项落实率 ≥ 80%
    - ✅ 团队满意度 ≥ 4.0/5.0
    - ✅ 跨团队协作顺畅

    ### 用户价值交付
    - ✅ 用户反馈积极率 ≥ 70%
    - ✅ 功能使用率达到预期
    - ✅ 用户问题响应及时
    - ✅ 产品价值持续提升
  </criteria>
</execution>
